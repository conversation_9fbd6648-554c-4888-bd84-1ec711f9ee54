#!/usr/bin/env python3
"""
Scalene Profile Analyzer
Parse profile.json và tạo báo cáo performance ngắn gọn
"""

import json
import sys
from datetime import datetime
from collections import defaultdict
import argparse

class ScaleneProfileAnalyzer:
    def __init__(self, profile_file):
        self.profile_file = profile_file
        self.profile_data = None
        self.performance_issues = []
        
        # Thresholds for performance issues
        self.thresholds = {
            'cpu_python_high': 1.0,      # >1% Python CPU
            'cpu_c_high': 5.0,            # >5% C/native CPU  
            'memory_allocation_high': 50, # >50MB allocation
            'memory_peak_high': 100,      # >100MB peak
            'gpu_usage_high': 20,         # >20% GPU usage
            'gpu_memory_high': 200,       # >200MB GPU memory
        }
    
    def load_profile(self):
        """Load và parse profile JSON"""
        try:
            with open(self.profile_file, 'r') as f:
                self.profile_data = json.load(f)
            print(f"✅ Loaded profile: {self.profile_file}")
            return True
        except Exception as e:
            print(f"❌ Error loading profile: {e}")
            return False
    
    def analyze_summary(self):
        """Phân tích tổng quan"""
        summary = {
            'elapsed_time': self.profile_data.get('elapsed_time_sec', 0),
            'alloc_samples': self.profile_data.get('alloc_samples', 0),
            'total_files': len(self.profile_data.get('files', {})),
        }
        
        print("=" * 60)
        print("📊 SCALENE PROFILE SUMMARY")
        print("=" * 60)
        print(f"⏱️  Total Runtime: {summary['elapsed_time']:.2f} seconds")
        print(f"📊 Allocation Samples: {summary['alloc_samples']}")
        print(f"📁 Files Profiled: {summary['total_files']}")
        
        return summary
    
    def analyze_performance_issues(self):
        """Phân tích performance issues"""
        issues = {
            'cpu_hotspots': [],
            'memory_hotspots': [],
            'gpu_hotspots': [],
            'functions_hotspots': []
        }
        
        files = self.profile_data.get('files', {})
        
        for file_path, file_data in files.items():
            # Analyze functions
            functions = file_data.get('functions', [])
            for func in functions:
                self._analyze_function_performance(func, file_path, issues)
            
            # Analyze lines
            lines = file_data.get('lines', [])
            for line_data in lines:
                self._analyze_line_performance(line_data, file_path, issues)
        
        return issues
    
    def _analyze_function_performance(self, func, file_path, issues):
        """Phân tích performance của function"""
        func_name = func.get('line', 'unknown')
        lineno = func.get('lineno', 0)
        
        # CPU analysis
        cpu_python = func.get('n_cpu_percent_python', 0)
        cpu_c = func.get('n_cpu_percent_c', 0)
        
        if cpu_python > self.thresholds['cpu_python_high']:
            issues['cpu_hotspots'].append({
                'type': 'function',
                'file': file_path,
                'function': func_name,
                'line': lineno,
                'cpu_python': cpu_python,
                'cpu_c': cpu_c,
                'severity': 'HIGH' if cpu_python > 5 else 'MEDIUM'
            })
        
        # Memory analysis
        malloc_mb = func.get('n_malloc_mb', 0)
        peak_mb = func.get('n_peak_mb', 0)
        
        if malloc_mb > self.thresholds['memory_allocation_high']:
            issues['memory_hotspots'].append({
                'type': 'function',
                'file': file_path,
                'function': func_name,
                'line': lineno,
                'malloc_mb': malloc_mb,
                'peak_mb': peak_mb,
                'mallocs': func.get('n_mallocs', 0),
                'severity': 'HIGH' if malloc_mb > 200 else 'MEDIUM'
            })
        
        # GPU analysis
        gpu_percent = func.get('n_gpu_percent', 0)
        gpu_memory = func.get('n_gpu_avg_memory_mb', 0)
        
        if gpu_percent > self.thresholds['gpu_usage_high'] or gpu_memory > self.thresholds['gpu_memory_high']:
            issues['gpu_hotspots'].append({
                'type': 'function',
                'file': file_path,
                'function': func_name,
                'line': lineno,
                'gpu_percent': gpu_percent,
                'gpu_memory_avg': gpu_memory,
                'gpu_memory_peak': func.get('n_gpu_peak_memory_mb', 0),
                'severity': 'HIGH' if gpu_percent > 50 else 'MEDIUM'
            })
    
    def _analyze_line_performance(self, line_data, file_path, issues):
        """Phân tích performance của line"""
        lineno = line_data.get('lineno', 0)
        line_content = line_data.get('line', '').strip()
        
        # Skip empty lines or comments
        if not line_content or line_content.startswith('#'):
            return
        
        # CPU analysis
        cpu_python = line_data.get('n_cpu_percent_python', 0)
        cpu_c = line_data.get('n_cpu_percent_c', 0)
        
        if cpu_python > self.thresholds['cpu_python_high']:
            issues['cpu_hotspots'].append({
                'type': 'line',
                'file': file_path,
                'line': lineno,
                'code': line_content[:80] + '...' if len(line_content) > 80 else line_content,
                'cpu_python': cpu_python,
                'cpu_c': cpu_c,
                'severity': 'HIGH' if cpu_python > 5 else 'MEDIUM'
            })
        
        # Memory analysis
        malloc_mb = line_data.get('n_malloc_mb', 0)
        peak_mb = line_data.get('n_peak_mb', 0)
        
        if malloc_mb > self.thresholds['memory_allocation_high']:
            issues['memory_hotspots'].append({
                'type': 'line',
                'file': file_path,
                'line': lineno,
                'code': line_content[:80] + '...' if len(line_content) > 80 else line_content,
                'malloc_mb': malloc_mb,
                'peak_mb': peak_mb,
                'mallocs': line_data.get('n_mallocs', 0),
                'severity': 'HIGH' if malloc_mb > 200 else 'MEDIUM'
            })
    
    def print_performance_report(self, issues):
        """In báo cáo performance"""
        
        # CPU Hotspots
        print("\n🔥 CPU PERFORMANCE HOTSPOTS")
        print("-" * 60)
        cpu_hotspots = sorted(issues['cpu_hotspots'], key=lambda x: x['cpu_python'], reverse=True)[:10]
        
        if cpu_hotspots:
            for i, hotspot in enumerate(cpu_hotspots, 1):
                severity_icon = "🔴" if hotspot['severity'] == 'HIGH' else "🟡"
                print(f"{severity_icon} #{i} Line {hotspot['line']}: {hotspot['cpu_python']:.2f}% Python CPU")
                if hotspot['type'] == 'function':
                    print(f"   Function: {hotspot['function']}")
                else:
                    print(f"   Code: {hotspot['code']}")
                print(f"   File: {hotspot['file'].split('/')[-1]}")
                print()
        else:
            print("✅ No significant CPU hotspots detected")
        
        # Memory Hotspots
        print("\n💾 MEMORY PERFORMANCE HOTSPOTS")
        print("-" * 60)
        memory_hotspots = sorted(issues['memory_hotspots'], key=lambda x: x['malloc_mb'], reverse=True)[:10]
        
        if memory_hotspots:
            for i, hotspot in enumerate(memory_hotspots, 1):
                severity_icon = "🔴" if hotspot['severity'] == 'HIGH' else "🟡"
                print(f"{severity_icon} #{i} Line {hotspot['line']}: {hotspot['malloc_mb']:.2f}MB allocated")
                print(f"   Peak: {hotspot['peak_mb']:.2f}MB, Allocations: {hotspot['mallocs']}")
                if hotspot['type'] == 'function':
                    print(f"   Function: {hotspot['function']}")
                else:
                    print(f"   Code: {hotspot['code']}")
                print(f"   File: {hotspot['file'].split('/')[-1]}")
                print()
        else:
            print("✅ No significant memory hotspots detected")
        
        # GPU Hotspots
        print("\n🎮 GPU PERFORMANCE HOTSPOTS")
        print("-" * 60)
        gpu_hotspots = sorted(issues['gpu_hotspots'], key=lambda x: x['gpu_percent'], reverse=True)[:10]
        
        if gpu_hotspots:
            for i, hotspot in enumerate(gpu_hotspots, 1):
                severity_icon = "🔴" if hotspot['severity'] == 'HIGH' else "🟡"
                print(f"{severity_icon} #{i} Line {hotspot['line']}: {hotspot['gpu_percent']:.1f}% GPU")
                print(f"   GPU Memory: {hotspot['gpu_memory_avg']:.1f}MB avg, {hotspot['gpu_memory_peak']:.1f}MB peak")
                if hotspot['type'] == 'function':
                    print(f"   Function: {hotspot['function']}")
                print(f"   File: {hotspot['file'].split('/')[-1]}")
                print()
        else:
            print("✅ No significant GPU hotspots detected")
    
    def generate_recommendations(self, issues):
        """Tạo recommendations"""
        print("\n💡 OPTIMIZATION RECOMMENDATIONS")
        print("-" * 60)
        
        recommendations = []
        
        # CPU recommendations
        cpu_count = len(issues['cpu_hotspots'])
        if cpu_count > 0:
            recommendations.append(f"🔥 {cpu_count} CPU hotspots detected:")
            recommendations.append("   - Profile specific functions with py-spy")
            recommendations.append("   - Consider async/threading for blocking operations")
            recommendations.append("   - Optimize algorithms in high-CPU lines")
        
        # Memory recommendations
        memory_count = len(issues['memory_hotspots'])
        if memory_count > 0:
            recommendations.append(f"💾 {memory_count} memory hotspots detected:")
            recommendations.append("   - Implement object pooling for large allocations")
            recommendations.append("   - Check for memory leaks in camera/video processing")
            recommendations.append("   - Consider streaming instead of loading full data")
        
        # GPU recommendations
        gpu_count = len(issues['gpu_hotspots'])
        if gpu_count > 0:
            recommendations.append(f"🎮 {gpu_count} GPU hotspots detected:")
            recommendations.append("   - Implement GPU memory pooling")
            recommendations.append("   - Optimize video decoding pipeline")
            recommendations.append("   - Monitor GPU memory leaks")
        
        if recommendations:
            for rec in recommendations:
                print(rec)
        else:
            print("✅ Performance looks good! No major issues detected.")
        
        # General recommendations
        print("\n📋 GENERAL RECOMMENDATIONS:")
        print("   - Run profiling during peak camera loading scenarios")
        print("   - Monitor memory usage when loading multiple cameras")
        print("   - Consider implementing the Camera Queue Manager from the guide")
        print("   - Set up continuous performance monitoring in production")
    
    def save_report(self, issues, output_file):
        """Lưu báo cáo ra file"""
        report = {
            'generated_at': datetime.now().isoformat(),
            'profile_file': self.profile_file,
            'summary': {
                'elapsed_time': self.profile_data.get('elapsed_time_sec', 0),
                'alloc_samples': self.profile_data.get('alloc_samples', 0),
            },
            'issues': issues,
            'thresholds': self.thresholds
        }
        
        try:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Detailed report saved to: {output_file}")
        except Exception as e:
            print(f"❌ Error saving report: {e}")
    
    def run_analysis(self, output_file=None):
        """Chạy phân tích hoàn chỉnh"""
        if not self.load_profile():
            return False
        
        # Summary
        self.analyze_summary()
        
        # Performance analysis
        issues = self.analyze_performance_issues()
        
        # Print report
        self.print_performance_report(issues)
        
        # Recommendations
        self.generate_recommendations(issues)
        
        # Save detailed report
        if output_file:
            self.save_report(issues, output_file)
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Analyze Scalene profile.json for performance issues")
    parser.add_argument('profile_file', help='Path to profile.json file')
    parser.add_argument('--output', '-o', help='Output file for detailed report (JSON)')
    parser.add_argument('--cpu-threshold', type=float, default=1.0, help='CPU threshold percentage (default: 1.0)')
    parser.add_argument('--memory-threshold', type=float, default=50, help='Memory threshold MB (default: 50)')
    parser.add_argument('--gpu-threshold', type=float, default=20, help='GPU threshold percentage (default: 20)')
    
    args = parser.parse_args()
    
    # Create analyzer
    analyzer = ScaleneProfileAnalyzer(args.profile_file)
    
    # Update thresholds if provided
    analyzer.thresholds['cpu_python_high'] = args.cpu_threshold
    analyzer.thresholds['memory_allocation_high'] = args.memory_threshold
    analyzer.thresholds['gpu_usage_high'] = args.gpu_threshold
    
    # Run analysis
    success = analyzer.run_analysis(args.output)
    
    if success:
        print(f"\n✅ Analysis completed successfully!")
    else:
        print(f"\n❌ Analysis failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
