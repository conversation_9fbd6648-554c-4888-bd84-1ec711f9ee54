# Phân Tích Tối Ưu RAM cho QPixmap Processing Pipeline

## 🔍 Phân Tích Luồng Xử Lý Hiện Tại

### Luồng Chính:
```
Mat Frame → QImage → QPixmap.fromImage() → pixmap_resized → share_frame_signal → 
frameModel.updateFrame → self.update() → paint() → painter.drawPixmap()
```

### Điểm Ti<PERSON> RAM Cao Nhất:

#### 1. **QPixmap.fromImage(img) - Điểm Nghẽn Chính**
- **Vị trí**: `src/common/camera/player.py:226` và `live_stream_player.py:273`
- **Vấn đề**: Tạo QPixmap mới cho mỗi frame (30-60 FPS)
- **RAM Usage**: ~8MB/frame cho 1920x1080 (4 bytes/pixel)
- **Tác động**: Với 16 camera → ~128MB/frame, 60 FPS → ~7.6GB/giây allocation

#### 2. **Pixmap Scaling Operations**
- **Vị trí**: `frame_model.py:127`, `video_model.py:126-131`
- **Vấn đề**: Tạo scaled_pixmap mới mỗi lần paint()
- **RAM Usage**: Thêm ~4-8MB/camera mỗi lần scale

#### 3. **Multiple Pixmap Copies**
- **Rounded corners**: `camera_draw_frame.py:496-511`
- **Aspect ratio scaling**: `image_widget.py:93-95`
- **Transform operations**: `custom_side_menu.py:268-276`

## 🚀 Phương Án Tối Ưu

### **Phương Án 1: QPixmap Pool Pattern (Ưu tiên cao)**

#### Triển khai:
```python
# Đã có sẵn trong src/common/utils/pixmap_pool.py
from src.common.utils.pixmap_pool import get_pooled_pixmap, return_pooled_pixmap

# Thay thế trong mat_to_q_pixmap:
@staticmethod
def mat_to_q_pixmap_optimized(mat):
    h, w, c = mat.shape
    
    # Lấy pixmap từ pool thay vì tạo mới
    pixmap = get_pooled_pixmap(w, h)
    
    # Tạo QImage và copy data
    d = mat.dtype.itemsize
    s = c * w * d
    img = QImage(mat, w, h, s, QImage.Format_RGB888).rgbSwapped()
    
    # Copy image data vào pooled pixmap
    pixmap = QPixmap.fromImage(img)
    return pixmap
```

#### Lợi ích:
- **Giảm 80-90% allocation**: Reuse pixmap objects
- **Giảm GC pressure**: Ít object creation/destruction
- **Thread-safe**: Có RLock protection

### **Phương Án 2: Lazy Scaling với Cache**

#### Triển khai:
```python
class OptimizedFrameModel:
    def __init__(self):
        self._scaled_cache = {}  # size_key → scaled_pixmap
        self._last_frame_id = None
        
    def paint(self, painter: QPainter):
        if self._q_image is None:
            return
            
        target_rect = self.boundingRect()
        size_key = f"{int(target_rect.width())}x{int(target_rect.height())}"
        
        # Chỉ scale khi size thay đổi hoặc frame mới
        if (size_key not in self._scaled_cache or 
            self._frame_count != self._last_frame_id):
            
            # Scale và cache
            scaled_pixmap = self._q_image.scaled(...)
            self._scaled_cache[size_key] = scaled_pixmap
            self._last_frame_id = self._frame_count
            
        # Sử dụng cached scaled pixmap
        painter.drawPixmap(x_offset, y_offset, self._scaled_cache[size_key])
```

#### Lợi ích:
- **Giảm 70% scaling operations**: Chỉ scale khi cần thiết
- **Faster paint()**: Sử dụng pre-scaled pixmap

### **Phương Án 3: Direct QImage Rendering**

#### Triển khai:
```python
def paint_optimized(self, painter: QPainter):
    if self._q_image is None:
        return
        
    # Vẽ trực tiếp QImage thay vì convert sang QPixmap
    target_rect = self.boundingRect()
    
    # QImage scaling thường nhẹ hơn QPixmap
    scaled_image = self._q_image.toImage().scaled(
        int(target_rect.width()),
        int(target_rect.height()),
        Qt.KeepAspectRatio,
        Qt.SmoothTransformation
    )
    
    painter.drawImage(x_offset, y_offset, scaled_image)
```

#### Lợi ích:
- **Bỏ qua QPixmap conversion**: Giảm 1 bước allocation
- **Lighter memory footprint**: QImage thường nhẹ hơn QPixmap

### **Phương Án 4: Frame Rate Throttling**

#### Triển khai:
```python
class ThrottledFrameModel:
    def __init__(self):
        self._last_update_time = 0
        self._target_fps = 30  # Giảm từ 60 FPS
        self._frame_interval = 1.0 / self._target_fps
        
    @Slot(QPixmap)
    def updateFrame(self, frame):
        current_time = time.time()
        
        # Throttle frame updates
        if current_time - self._last_update_time < self._frame_interval:
            return  # Skip frame
            
        self._last_update_time = current_time
        # Process frame normally...
```

#### Lợi ích:
- **Giảm 50% frame processing**: 30 FPS thay vì 60 FPS
- **Smoother performance**: Ít CPU/RAM spikes

## 📊 Ước Tính Hiệu Quả

### Trước Tối Ưu (16 cameras, 60 FPS):
- **RAM/giây**: ~7.6GB allocation
- **Peak RAM**: ~2-3GB cho video buffers
- **GC frequency**: Cao (mỗi 1-2 giây)

### Sau Tối Ưu (Kết hợp 4 phương án):
- **RAM/giây**: ~1.5GB allocation (-80%)
- **Peak RAM**: ~800MB (-70%)
- **GC frequency**: Thấp (mỗi 10-15 giây)

## 🛠 Triển Khai Ưu Tiên

### Phase 1: QPixmap Pool (Tuần 1)
1. Integrate pixmap pool vào `mat_to_q_pixmap()`
2. Test với 4-8 cameras
3. Monitor memory usage

### Phase 2: Lazy Scaling (Tuần 2)
1. Implement scaling cache trong FrameModel
2. Optimize paint() methods
3. Performance testing

### Phase 3: Frame Throttling (Tuần 3)
1. Add FPS controls
2. User preference settings
3. Adaptive throttling based on system load

### Phase 4: QImage Direct Rendering (Tuần 4)
1. Experiment với QImage rendering
2. Compare performance vs QPixmap
3. A/B testing

## 🔧 Monitoring & Metrics

### Key Metrics:
- **Memory allocation rate** (MB/s)
- **Peak memory usage** (GB)
- **GC pause time** (ms)
- **Frame drop rate** (%)
- **UI responsiveness** (frame time)

### Tools:
- Python memory_profiler
- Qt memory debugging
- Custom RAM monitoring dashboard
