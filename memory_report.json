{"analysis_time": 18.08333683013916, "memory_summary": {"total_allocated_mb": 180.08697032928467, "total_peak_mb": 170.08370304107666, "total_growth_mb": 170.08370304107666, "allocation_count": 7}, "by_file": {"VMS.py": {"allocated": 0, "peak": 0, "count": 0}, "api_client.py": {"allocated": 0, "peak": 0, "count": 0}, "pyav_wrapper.py": {"allocated": 0, "peak": 0, "count": 0}, "video_player_manager.py": {"allocated": 0, "peak": 0, "count": 0}, "key_board_manager.py": {"allocated": 0, "peak": 0, "count": 0}, "device_controller.py": {"allocated": 140.08075618743896, "peak": 130.07748889923096, "count": 3}, "message_processor.py": {"allocated": 0, "peak": 0, "count": 0}, "websocket_client.py": {"allocated": 0, "peak": 0, "count": 0}, "custom_side_menu.py": {"allocated": 0, "peak": 0, "count": 0}, "actionable_title_bar.py": {"allocated": 0, "peak": 0, "count": 0}, "base_dialog.py": {"allocated": 0, "peak": 0, "count": 0}, "image_widget.py": {"allocated": 0, "peak": 0, "count": 0}, "camera_grid_widget.py": {"allocated": 10.004361152648926, "peak": 10.004361152648926, "count": 1}, "export_video_dialog.py": {"allocated": 0, "peak": 0, "count": 0}, "main_tree_view_widget.py": {"allocated": 0, "peak": 0, "count": 0}, "device_screen.py": {"allocated": 0, "peak": 0, "count": 0}, "list_custom_widgets.py": {"allocated": 0, "peak": 0, "count": 0}, "server_item.py": {"allocated": 0, "peak": 0, "count": 0}, "base_setting_tab.py": {"allocated": 10.000007629394531, "peak": 10.000007629394531, "count": 1}, "user_group_tableview.py": {"allocated": 10.000046730041504, "peak": 10.000046730041504, "count": 1}, "users_tableview.py": {"allocated": 10.001798629760742, "peak": 10.001798629760742, "count": 1}, "file.py": {"allocated": 0, "peak": 0, "count": 0}}, "top_functions": [{"file": "device_controller.py", "function": "ListModel.add_item_after_index", "line": 239, "malloc_mb": 120.08059787750244, "peak_mb": 110.07733058929443, "growth_mb": 110.07733058929443, "mallocs": 1, "avg_mb": 10.003267288208008, "python_fraction": 0.3510067007590115}, {"file": "camera_grid_widget.py", "function": "CameraGridWidget.loadQML", "line": 32, "malloc_mb": 10.004361152648926, "peak_mb": 10.004361152648926, "growth_mb": 10.004361152648926, "mallocs": 1, "avg_mb": 10.004361152648926, "python_fraction": 0.742543}, {"file": "users_tableview.py", "function": "UsersTableView.update_model", "line": 224, "malloc_mb": 10.001798629760742, "peak_mb": 10.001798629760742, "growth_mb": 10.001798629760742, "mallocs": 1, "avg_mb": 10.001798629760742, "python_fraction": 0.742299}, {"file": "device_controller.py", "function": "ListModel.data", "line": 108, "malloc_mb": 10.000140190124512, "peak_mb": 10.000140190124512, "growth_mb": 10.000140190124512, "mallocs": 1, "avg_mb": 10.000140190124512, "python_fraction": 0.071458}, {"file": "user_group_tableview.py", "function": "CustomLabelRoleTable.load_ui", "line": 695, "malloc_mb": 10.000046730041504, "peak_mb": 10.000046730041504, "growth_mb": 10.000046730041504, "mallocs": 1, "avg_mb": 10.000046730041504, "python_fraction": 0.56666}, {"file": "device_controller.py", "function": "ListModel.__init__", "line": 84, "malloc_mb": 10.000018119812012, "peak_mb": 10.000018119812012, "growth_mb": 10.000018119812012, "mallocs": 1, "avg_mb": 10.000018119812012, "python_fraction": 0.8384240000000001}, {"file": "base_setting_tab.py", "function": "BaseSettingTab.create_label", "line": 115, "malloc_mb": 10.000007629394531, "peak_mb": 10.000007629394531, "growth_mb": 10.000007629394531, "mallocs": 1, "avg_mb": 10.000007629394531, "python_fraction": 0.559838}], "large_allocations": [{"file": "device_controller.py", "function": "ListModel.add_item_after_index", "line": 239, "malloc_mb": 120.08059787750244, "peak_mb": 110.07733058929443, "growth_mb": 110.07733058929443, "mallocs": 1, "avg_mb": 10.003267288208008, "python_fraction": 0.3510067007590115}], "frequent_allocations": [], "system_memory": {"process_rss_mb": 70.875, "process_vms_mb": 398840.609375, "system_total_gb": 16.0, "system_available_gb": 6.2408447265625, "system_used_percent": 61.0}}