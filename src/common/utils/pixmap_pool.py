"""
QPixmap Pool để tối ưu memory usage trong video processing
Giảm allocation/deallocation liên tục của QPixmap objects
"""

from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import QSize
import threading
from typing import Dict, List, Optional
import weakref
import gc


class PixmapPool:
    """
    Pool pattern cho QPixmap để tránh allocation/deallocation liên tục
    
    Kịch bản sử dụng:
    - Camera frames được convert từ mat → QPixmap liên tục
    - Thay vì tạo mới QPixmap mỗi frame → reuse từ pool
    - Giảm memory fragmentation và GC pressure
    """
    
    def __init__(self, max_pool_size: int = 50):
        self._pool: Dict[str, List[QPixmap]] = {}  # size_key → [pixmap1, pixmap2, ...]
        self._max_pool_size = max_pool_size
        self._lock = threading.RLock()
        self._active_pixmaps: Dict[int, QSize] = {}  # id(pixmap) → size
        
    def _get_size_key(self, width: int, height: int) -> str:
        """Tạo key cho pool dựa trên kích thước"""
        return f"{width}x{height}"
    
    def get_pixmap(self, width: int, height: int) -> QPixmap:
        """
        Lấy QPixmap từ pool hoặc tạo mới nếu pool trống
        
        Args:
            width: Chiều rộng pixmap
            height: Chiều cao pixmap
            
        Returns:
            QPixmap với kích thước yêu cầu
        """
        size_key = self._get_size_key(width, height)
        
        with self._lock:
            # Kiểm tra pool có pixmap phù hợp không
            if size_key in self._pool and self._pool[size_key]:
                pixmap = self._pool[size_key].pop()
                self._active_pixmaps[id(pixmap)] = QSize(width, height)
                return pixmap
            
            # Tạo mới nếu pool trống
            pixmap = QPixmap(width, height)
            self._active_pixmaps[id(pixmap)] = QSize(width, height)
            return pixmap
    
    def return_pixmap(self, pixmap: QPixmap):
        """
        Trả QPixmap về pool để reuse
        
        Args:
            pixmap: QPixmap cần trả về pool
        """
        if pixmap.isNull():
            return
            
        pixmap_id = id(pixmap)
        
        with self._lock:
            # Kiểm tra pixmap có trong active list không
            if pixmap_id not in self._active_pixmaps:
                return
                
            size = self._active_pixmaps.pop(pixmap_id)
            size_key = self._get_size_key(size.width(), size.height())
            
            # Thêm vào pool nếu chưa đầy
            if size_key not in self._pool:
                self._pool[size_key] = []
                
            if len(self._pool[size_key]) < self._max_pool_size:
                # Clear pixmap content trước khi return về pool
                pixmap.fill()
                self._pool[size_key].append(pixmap)
    
    def get_pool_stats(self) -> Dict:
        """Lấy thống kê pool để monitoring"""
        with self._lock:
            stats = {
                'pool_sizes': {k: len(v) for k, v in self._pool.items()},
                'active_pixmaps': len(self._active_pixmaps),
                'total_pooled': sum(len(v) for v in self._pool.values())
            }
            return stats
    
    def cleanup_pool(self, max_size_per_bucket: int = 10):
        """
        Cleanup pool để tránh memory leak
        
        Args:
            max_size_per_bucket: Số lượng pixmap tối đa mỗi size bucket
        """
        with self._lock:
            for size_key in list(self._pool.keys()):
                bucket = self._pool[size_key]
                if len(bucket) > max_size_per_bucket:
                    # Giữ lại max_size_per_bucket pixmaps mới nhất
                    excess = bucket[:-max_size_per_bucket]
                    self._pool[size_key] = bucket[-max_size_per_bucket:]
                    
                    # Force cleanup excess pixmaps
                    for pixmap in excess:
                        del pixmap
                        
            # Force garbage collection
            gc.collect()


# Global pixmap pool instance
_pixmap_pool = PixmapPool(max_pool_size=30)


def get_pooled_pixmap(width: int, height: int) -> QPixmap:
    """
    Convenience function để lấy pixmap từ global pool
    
    Usage:
        pixmap = get_pooled_pixmap(1920, 1080)
        # ... sử dụng pixmap
        return_pooled_pixmap(pixmap)
    """
    return _pixmap_pool.get_pixmap(width, height)


def return_pooled_pixmap(pixmap: QPixmap):
    """
    Convenience function để trả pixmap về global pool
    """
    _pixmap_pool.return_pixmap(pixmap)


def get_pool_stats() -> Dict:
    """Lấy thống kê global pool"""
    return _pixmap_pool.get_pool_stats()


def cleanup_pixmap_pool():
    """Cleanup global pool"""
    _pixmap_pool.cleanup_pool()


class PixmapContext:
    """
    Context manager cho pixmap pool
    
    Usage:
        with PixmapContext(1920, 1080) as pixmap:
            # Sử dụng pixmap
            painter = QPainter(pixmap)
            # ...
        # Pixmap tự động được return về pool
    """
    
    def __init__(self, width: int, height: int):
        self.width = width
        self.height = height
        self.pixmap: Optional[QPixmap] = None
    
    def __enter__(self) -> QPixmap:
        self.pixmap = get_pooled_pixmap(self.width, self.height)
        return self.pixmap
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.pixmap:
            return_pooled_pixmap(self.pixmap)
            self.pixmap = None
