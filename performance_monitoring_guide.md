# Hướng Dẫn Performance Monitoring cho Python

## Tổng Quan

Tài liệu này hướng dẫn sử dụng **Scalene** cho development và **py-spy + psutil** cho production monitoring để tracking hiệu năng ứng dụng Python với overhead thấp nhất.

## Cài Đặt

### Development Environment
```bash
pip install scalene
```

### Production Environment
```bash
pip install py-spy psutil pynvml
```

## ⚠️ Cross-Platform Compatibility & Requirements

### ✅ **psutil** - Hoàn toàn cross-platform
- **Platforms**: Windows, macOS, Linux, FreeBSD, NetBSD, OpenBSD
- **Requirements**: Không cần setup gì thêm
- **Installation**: `pip install psutil` (works everywhere)
- **Note**: Pure Python với C extensions, tự động compile

### ✅ **py-spy** - Cross-platform với một số lưu ý
- **Platforms**: Windows, macOS, Linux
- **Requirements**:
  - **Linux**: <PERSON><PERSON> <PERSON>ể cần `sudo` cho một số operations
  - **macOS**: Có thể cần disable SIP (System Integrity Protection) cho full features
  - **Windows**: Works out of the box
- **Installation**: `pip install py-spy`
- **Note**: Precompiled binaries available cho tất cả platforms

### ⚠️ **pynvml** - Chỉ hoạt động khi có NVIDIA GPU
- **Platforms**: Windows, Linux, macOS (nếu có NVIDIA GPU)
- **Requirements**:
  - **NVIDIA GPU** phải có
  - **NVIDIA drivers** đã cài đặt
  - **CUDA toolkit** (optional nhưng recommended)
- **Installation**: `pip install pynvml` hoặc `pip install nvidia-ml-py`
- **Fallback**: Code sẽ gracefully handle khi không có GPU

### 🔧 **Setup Requirements Summary**

| Package | Windows | macOS | Linux | Special Requirements |
|---------|---------|-------|-------|---------------------|
| **psutil** | ✅ | ✅ | ✅ | None |
| **py-spy** | ✅ | ⚠️ | ⚠️ | May need admin privileges |
| **pynvml** | 🎯 | 🎯 | 🎯 | NVIDIA GPU + drivers required |

**Legend**: ✅ = Works perfectly, ⚠️ = May need extra setup, 🎯 = Hardware dependent

## 1. Development Monitoring với Scalene

### 1.1 Cài Đặt và Sử Dụng Cơ Bản

```bash
# Profile toàn bộ ứng dụng
scalene your_app.py

# Profile với reduced output (chỉ hiển thị lines quan trọng)
scalene --reduced-profile your_app.py

# Profile chỉ CPU
scalene --cpu your_app.py

# Profile CPU + GPU + Memory
scalene --cpu --gpu --memory your_app.py

# Export HTML report
scalene --html --outfile performance_report.html your_app.py
```

### 1.2 Programmatic Usage

```python
from scalene import scalene_profiler
import threading
import time

class PerformanceTracker:
    def __init__(self):
        self.is_profiling = False
    
    def start_profiling(self):
        """Bắt đầu profiling"""
        if not self.is_profiling:
            scalene_profiler.start()
            self.is_profiling = True
            print("✅ Scalene profiling started")
    
    def stop_profiling(self):
        """Dừng profiling"""
        if self.is_profiling:
            scalene_profiler.stop()
            self.is_profiling = False
            print("⏹️ Scalene profiling stopped")
    
    def profile_function(self, func):
        """Decorator để profile specific function"""
        def wrapper(*args, **kwargs):
            self.start_profiling()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                self.stop_profiling()
        return wrapper

# Sử dụng
tracker = PerformanceTracker()

@tracker.profile_function
def heavy_computation():
    # Simulate heavy work
    for i in range(1000000):
        _ = i ** 2

# Hoặc sử dụng context manager
from scalene.scalene_profiler import enable_profiling

with enable_profiling():
    # Code cần profile
    heavy_computation()
```

### 1.3 Profile Specific Functions

```python
# Không cần import profile!
@profile
def cpu_intensive_task():
    """Function chiếm nhiều CPU"""
    result = []
    for i in range(100000):
        result.append(i ** 2)
    return result

@profile  
def memory_intensive_task():
    """Function chiếm nhiều memory"""
    big_list = [i for i in range(1000000)]
    return big_list

@profile
def io_blocking_task():
    """Function có I/O blocking"""
    import time
    time.sleep(2)  # Simulate I/O wait
    return "completed"

# Chạy với: scalene your_script.py
```

## 2. Production Monitoring với py-spy + psutil

### 2.1 py-spy Usage

```bash
# Profile running process
py-spy record -o profile.svg --pid 12345

# Profile new process  
py-spy record -o profile.svg -- python your_app.py

# Real-time monitoring
py-spy top --pid 12345

# Profile for specific duration
py-spy record -o profile.svg --duration 60 --pid 12345
```

### 2.2 Real-time Thread Monitoring Script

```python
import psutil
import threading
import time
import json
from datetime import datetime
from collections import defaultdict
import os

class ProductionMonitor:
    def __init__(self, target_pid=None):
        self.target_pid = target_pid or os.getpid()
        self.process = psutil.Process(self.target_pid)
        self.monitoring = False
        self.metrics_history = []
        
    def get_thread_info(self):
        """Lấy thông tin chi tiết về threads"""
        try:
            threads = self.process.threads()
            thread_info = []
            
            for thread in threads:
                thread_info.append({
                    'thread_id': thread.id,
                    'user_time': thread.user_time,
                    'system_time': thread.system_time,
                    'total_time': thread.user_time + thread.system_time
                })
            
            return sorted(thread_info, key=lambda x: x['total_time'], reverse=True)
        except psutil.AccessDenied:
            return []
    
    def get_resource_usage(self):
        """Lấy thông tin sử dụng tài nguyên"""
        try:
            # Process metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            # System metrics
            system_cpu = psutil.cpu_percent(percpu=True)
            system_memory = psutil.virtual_memory()
            
            # I/O metrics
            io_counters = self.process.io_counters()
            
            # Network metrics (system-wide)
            net_io = psutil.net_io_counters()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'process': {
                    'pid': self.target_pid,
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_info.rss / 1024 / 1024,
                    'memory_percent': memory_percent,
                    'threads_count': self.process.num_threads(),
                    'open_files': len(self.process.open_files()),
                    'connections': len(self.process.connections()),
                },
                'system': {
                    'cpu_per_core': system_cpu,
                    'cpu_avg': sum(system_cpu) / len(system_cpu),
                    'memory_percent': system_memory.percent,
                    'memory_available_mb': system_memory.available / 1024 / 1024,
                },
                'io': {
                    'read_bytes': io_counters.read_bytes,
                    'write_bytes': io_counters.write_bytes,
                    'read_count': io_counters.read_count,
                    'write_count': io_counters.write_count,
                },
                'network': {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv,
                },
                'threads': self.get_thread_info()
            }
        except Exception as e:
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    def detect_bottlenecks(self, metrics):
        """Phát hiện nghẽn cổ chai"""
        bottlenecks = []

        # CPU bottleneck
        if metrics.get('process', {}).get('cpu_percent', 0) > 80:
            bottlenecks.append({
                'type': 'CPU_HIGH',
                'severity': 'HIGH',
                'message': f"Process CPU usage: {metrics['process']['cpu_percent']:.1f}%",
                'suggestion': 'Kiểm tra các vòng lặp, tính toán phức tạp'
            })

        # Memory bottleneck
        if metrics.get('process', {}).get('memory_percent', 0) > 70:
            bottlenecks.append({
                'type': 'MEMORY_HIGH',
                'severity': 'HIGH',
                'message': f"Process memory usage: {metrics['process']['memory_percent']:.1f}%",
                'suggestion': 'Kiểm tra memory leaks, large objects'
            })

        # System memory bottleneck
        if metrics.get('system', {}).get('memory_percent', 0) > 85:
            bottlenecks.append({
                'type': 'SYSTEM_MEMORY_HIGH',
                'severity': 'CRITICAL',
                'message': f"System memory usage: {metrics['system']['memory_percent']:.1f}%",
                'suggestion': 'Hệ thống thiếu RAM, cần tối ưu hoặc upgrade'
            })

        # Too many threads
        if metrics.get('process', {}).get('threads_count', 0) > 50:
            bottlenecks.append({
                'type': 'THREAD_COUNT_HIGH',
                'severity': 'MEDIUM',
                'message': f"Thread count: {metrics['process']['threads_count']}",
                'suggestion': 'Kiểm tra thread pool, async operations'
            })

        # I/O bottleneck (high read/write operations)
        io_ops = (metrics.get('io', {}).get('read_count', 0) +
                 metrics.get('io', {}).get('write_count', 0))
        if io_ops > 1000:  # per second
            bottlenecks.append({
                'type': 'IO_HIGH',
                'severity': 'MEDIUM',
                'message': f"High I/O operations: {io_ops}",
                'suggestion': 'Kiểm tra database queries, file operations'
            })

        return bottlenecks

    def detect_ui_blocking(self, metrics_window):
        """Phát hiện UI blocking patterns"""
        if len(metrics_window) < 3:
            return []

        ui_issues = []

        # Kiểm tra CPU spikes
        cpu_values = [m.get('process', {}).get('cpu_percent', 0) for m in metrics_window[-5:]]
        if any(cpu > 90 for cpu in cpu_values):
            ui_issues.append({
                'type': 'UI_BLOCKING_CPU',
                'severity': 'HIGH',
                'message': 'CPU spike detected - có thể block UI',
                'suggestion': 'Chuyển heavy computation sang background thread'
            })

        # Kiểm tra thread count changes
        thread_counts = [m.get('process', {}).get('threads_count', 0) for m in metrics_window[-3:]]
        if len(set(thread_counts)) == 1 and thread_counts[0] == 1:
            ui_issues.append({
                'type': 'UI_SINGLE_THREAD',
                'severity': 'MEDIUM',
                'message': 'Ứng dụng chạy single-thread',
                'suggestion': 'Sử dụng threading/asyncio cho UI responsiveness'
            })

        return ui_issues

    def start_monitoring(self, interval=1, duration=None):
        """Bắt đầu monitoring"""
        self.monitoring = True
        start_time = time.time()

        print(f"🔍 Bắt đầu monitoring process {self.target_pid}")
        print(f"📊 Interval: {interval}s, Duration: {duration or 'unlimited'}s")

        try:
            while self.monitoring:
                if duration and (time.time() - start_time) > duration:
                    break

                metrics = self.get_resource_usage()
                self.metrics_history.append(metrics)

                # Detect issues
                bottlenecks = self.detect_bottlenecks(metrics)
                ui_issues = self.detect_ui_blocking(self.metrics_history)

                # Print real-time info
                self._print_realtime_stats(metrics, bottlenecks, ui_issues)

                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n⏹️ Monitoring stopped by user")
        finally:
            self.monitoring = False

    def _print_realtime_stats(self, metrics, bottlenecks, ui_issues):
        """In thống kê real-time"""
        os.system('clear' if os.name == 'posix' else 'cls')

        print("=" * 80)
        print(f"🔍 PRODUCTION MONITORING - PID: {self.target_pid}")
        print(f"⏰ {metrics.get('timestamp', 'N/A')}")
        print("=" * 80)

        # Process stats
        process = metrics.get('process', {})
        print(f"📊 PROCESS STATS:")
        print(f"   CPU: {process.get('cpu_percent', 0):.1f}%")
        print(f"   Memory: {process.get('memory_mb', 0):.1f} MB ({process.get('memory_percent', 0):.1f}%)")
        print(f"   Threads: {process.get('threads_count', 0)}")
        print(f"   Open Files: {process.get('open_files', 0)}")

        # System stats
        system = metrics.get('system', {})
        print(f"\n🖥️ SYSTEM STATS:")
        print(f"   CPU Average: {system.get('cpu_avg', 0):.1f}%")
        print(f"   Memory: {system.get('memory_percent', 0):.1f}%")
        print(f"   Available: {system.get('memory_available_mb', 0):.1f} MB")

        # Top threads
        threads = metrics.get('threads', [])[:3]
        if threads:
            print(f"\n🧵 TOP THREADS:")
            for i, thread in enumerate(threads, 1):
                print(f"   {i}. Thread {thread['thread_id']}: {thread['total_time']:.2f}s")

        # Bottlenecks
        if bottlenecks:
            print(f"\n⚠️ BOTTLENECKS DETECTED:")
            for bottleneck in bottlenecks:
                severity_icon = "🔴" if bottleneck['severity'] == 'CRITICAL' else "🟡" if bottleneck['severity'] == 'HIGH' else "🟢"
                print(f"   {severity_icon} {bottleneck['message']}")
                print(f"      💡 {bottleneck['suggestion']}")

        # UI Issues
        if ui_issues:
            print(f"\n🖱️ UI BLOCKING ISSUES:")
            for issue in ui_issues:
                print(f"   ⚠️ {issue['message']}")
                print(f"      💡 {issue['suggestion']}")

        print("\n" + "=" * 80)
        print("Press Ctrl+C to stop monitoring")

    def save_report(self, filename="performance_report.json"):
        """Lưu báo cáo"""
        report = {
            'monitoring_summary': {
                'total_samples': len(self.metrics_history),
                'duration_seconds': len(self.metrics_history),
                'target_pid': self.target_pid
            },
            'metrics_history': self.metrics_history
        }

        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)

        print(f"📄 Report saved to {filename}")

# Sử dụng
if __name__ == "__main__":
    monitor = ProductionMonitor()
    monitor.start_monitoring(interval=1, duration=60)
    monitor.save_report()
```

## 3. GPU Monitoring

### 3.1 NVIDIA GPU Monitoring

```python
import pynvml
import time

class GPUMonitor:
    def __init__(self):
        try:
            pynvml.nvmlInit()
            self.gpu_count = pynvml.nvmlDeviceGetCount()
            self.available = True
            print(f"✅ NVIDIA GPU detected: {self.gpu_count} GPU(s)")
        except Exception as e:
            self.available = False
            print(f"⚠️ NVIDIA GPU not available: {str(e)}")
            print("💡 This is normal if you don't have NVIDIA GPU or drivers not installed")

    def get_gpu_metrics(self):
        """Lấy thông tin GPU"""
        if not self.available:
            return []

        gpu_metrics = []
        for i in range(self.gpu_count):
            try:
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)

                # GPU utilization
                util = pynvml.nvmlDeviceGetUtilizationRates(handle)

                # Memory usage
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)

                # Temperature
                temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)

                # Power usage
                power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # Convert to watts

                gpu_metrics.append({
                    'gpu_id': i,
                    'name': pynvml.nvmlDeviceGetName(handle).decode('utf-8'),
                    'utilization_gpu': util.gpu,
                    'utilization_memory': util.memory,
                    'memory_used_mb': mem_info.used / 1024 / 1024,
                    'memory_total_mb': mem_info.total / 1024 / 1024,
                    'memory_percent': (mem_info.used / mem_info.total) * 100,
                    'temperature_c': temp,
                    'power_watts': power
                })
            except Exception as e:
                gpu_metrics.append({'gpu_id': i, 'error': str(e)})

        return gpu_metrics

    def detect_gpu_bottlenecks(self, gpu_metrics):
        """Phát hiện GPU bottlenecks"""
        bottlenecks = []

        for gpu in gpu_metrics:
            if 'error' in gpu:
                continue

            # High GPU utilization
            if gpu['utilization_gpu'] > 90:
                bottlenecks.append({
                    'type': 'GPU_HIGH_UTIL',
                    'severity': 'HIGH',
                    'message': f"GPU {gpu['gpu_id']} utilization: {gpu['utilization_gpu']}%",
                    'suggestion': 'GPU đang overload, cần tối ưu compute operations'
                })

            # High memory usage
            if gpu['memory_percent'] > 85:
                bottlenecks.append({
                    'type': 'GPU_HIGH_MEMORY',
                    'severity': 'HIGH',
                    'message': f"GPU {gpu['gpu_id']} memory: {gpu['memory_percent']:.1f}%",
                    'suggestion': 'GPU memory gần hết, giảm batch size hoặc model size'
                })

            # High temperature
            if gpu['temperature_c'] > 80:
                bottlenecks.append({
                    'type': 'GPU_HIGH_TEMP',
                    'severity': 'MEDIUM',
                    'message': f"GPU {gpu['gpu_id']} temperature: {gpu['temperature_c']}°C",
                    'suggestion': 'GPU quá nóng, kiểm tra cooling system'
                })

        return bottlenecks

# Tích hợp GPU monitoring vào ProductionMonitor
class EnhancedProductionMonitor(ProductionMonitor):
    def __init__(self, target_pid=None):
        super().__init__(target_pid)
        self.gpu_monitor = GPUMonitor()

    def get_resource_usage(self):
        """Override để thêm GPU metrics"""
        metrics = super().get_resource_usage()

        # Thêm GPU metrics
        gpu_metrics = self.gpu_monitor.get_gpu_metrics()
        metrics['gpu'] = gpu_metrics

        return metrics

    def detect_bottlenecks(self, metrics):
        """Override để thêm GPU bottleneck detection"""
        bottlenecks = super().detect_bottlenecks(metrics)

        # Thêm GPU bottlenecks
        gpu_metrics = metrics.get('gpu', [])
        gpu_bottlenecks = self.gpu_monitor.detect_gpu_bottlenecks(gpu_metrics)
        bottlenecks.extend(gpu_bottlenecks)

        return bottlenecks
```

## 4. Thread-Specific Monitoring

### 4.1 Advanced Thread Analysis

```python
import threading
import sys
import traceback
from collections import defaultdict

class ThreadAnalyzer:
    def __init__(self):
        self.thread_stats = defaultdict(dict)
        self.monitoring = False

    def get_all_threads(self):
        """Lấy thông tin tất cả threads"""
        threads_info = []

        for thread_id, frame in sys._current_frames().items():
            thread = None
            for t in threading.enumerate():
                if t.ident == thread_id:
                    thread = t
                    break

            if thread:
                # Get stack trace
                stack = traceback.format_stack(frame)

                threads_info.append({
                    'thread_id': thread_id,
                    'name': thread.name,
                    'daemon': thread.daemon,
                    'alive': thread.is_alive(),
                    'stack_trace': stack,
                    'current_function': self._get_current_function(stack)
                })

        return threads_info

    def _get_current_function(self, stack):
        """Lấy function hiện tại từ stack trace"""
        if len(stack) >= 2:
            # Get the last meaningful frame (not this function)
            frame_line = stack[-2]
            # Extract function name from frame
            if 'in ' in frame_line:
                return frame_line.split('in ')[-1].split('\n')[0].strip()
        return "unknown"

    def detect_blocking_threads(self, threads_info):
        """Phát hiện threads có thể block UI"""
        blocking_issues = []

        main_thread = None
        worker_threads = []

        for thread in threads_info:
            if thread['name'] == 'MainThread':
                main_thread = thread
            else:
                worker_threads.append(thread)

        # Kiểm tra main thread
        if main_thread:
            current_func = main_thread['current_function']

            # Detect blocking operations in main thread
            blocking_patterns = [
                'time.sleep', 'requests.get', 'requests.post',
                'socket.recv', 'file.read', 'database.execute',
                'subprocess.run', 'os.system'
            ]

            for pattern in blocking_patterns:
                if pattern in current_func.lower():
                    blocking_issues.append({
                        'type': 'MAIN_THREAD_BLOCKING',
                        'severity': 'HIGH',
                        'message': f'Main thread blocked by: {current_func}',
                        'suggestion': f'Move {pattern} to background thread'
                    })

        # Kiểm tra too many threads
        if len(worker_threads) > 20:
            blocking_issues.append({
                'type': 'TOO_MANY_THREADS',
                'severity': 'MEDIUM',
                'message': f'Too many worker threads: {len(worker_threads)}',
                'suggestion': 'Use thread pool or async operations'
            })

        return blocking_issues

    def monitor_threads(self, interval=2):
        """Monitor threads continuously"""
        self.monitoring = True

        while self.monitoring:
            threads_info = self.get_all_threads()
            blocking_issues = self.detect_blocking_threads(threads_info)

            print("\n" + "="*60)
            print("🧵 THREAD ANALYSIS")
            print("="*60)

            print(f"📊 Total Threads: {len(threads_info)}")

            for thread in threads_info:
                status = "🟢" if thread['alive'] else "🔴"
                daemon_status = "👻" if thread['daemon'] else "👤"
                print(f"{status} {daemon_status} {thread['name']} (ID: {thread['thread_id']})")
                print(f"   📍 Current: {thread['current_function']}")

            if blocking_issues:
                print(f"\n⚠️ BLOCKING ISSUES:")
                for issue in blocking_issues:
                    print(f"   🚫 {issue['message']}")
                    print(f"      💡 {issue['suggestion']}")

            time.sleep(interval)

    def stop_monitoring(self):
        """Stop thread monitoring"""
        self.monitoring = False
```

## 5. Comprehensive Monitoring Script

### 5.1 All-in-One Monitor

```python
#!/usr/bin/env python3
"""
Comprehensive Performance Monitor
Combines Scalene (development) and py-spy + psutil (production)
"""

import argparse
import os
import sys
import time
import subprocess
from pathlib import Path

class ComprehensiveMonitor:
    def __init__(self):
        self.mode = None
        self.target_pid = None
        self.target_script = None

    def run_scalene_development(self, script_path, options=None):
        """Chạy Scalene cho development"""
        print("🔬 Running Scalene Development Profiling...")

        cmd = ['scalene']

        # Add options
        if options:
            if 'reduced' in options:
                cmd.append('--reduced-profile')
            if 'html' in options:
                cmd.append('--html')
                cmd.extend(['--outfile', 'scalene_report.html'])
            if 'cpu_only' in options:
                cmd.append('--cpu')

        cmd.append(script_path)

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Scalene profiling completed")
                if '--html' in cmd:
                    print("📄 HTML report saved to scalene_report.html")
            else:
                print(f"❌ Scalene error: {result.stderr}")
        except FileNotFoundError:
            print("❌ Scalene not installed. Run: pip install scalene")

    def run_pyspy_production(self, pid, duration=60):
        """Chạy py-spy cho production"""
        print(f"🔍 Running py-spy Production Profiling for PID {pid}...")

        # Generate flame graph
        cmd = [
            'py-spy', 'record',
            '-o', f'pyspy_profile_{pid}.svg',
            '--duration', str(duration),
            '--pid', str(pid)
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ py-spy profiling completed")
                print(f"📄 Flame graph saved to pyspy_profile_{pid}.svg")
            else:
                print(f"❌ py-spy error: {result.stderr}")
        except FileNotFoundError:
            print("❌ py-spy not installed. Run: pip install py-spy")

    def run_comprehensive_monitoring(self, pid=None, duration=60):
        """Chạy comprehensive monitoring"""
        print("🚀 Starting Comprehensive Production Monitoring...")

        # Start enhanced monitoring
        monitor = EnhancedProductionMonitor(pid)
        thread_analyzer = ThreadAnalyzer()

        # Start thread monitoring in background
        import threading
        thread_monitor_thread = threading.Thread(
            target=thread_analyzer.monitor_threads,
            args=(5,),  # 5 second interval
            daemon=True
        )
        thread_monitor_thread.start()

        try:
            # Start main monitoring
            monitor.start_monitoring(interval=1, duration=duration)
            monitor.save_report(f"comprehensive_report_{pid or 'current'}.json")

        finally:
            thread_analyzer.stop_monitoring()

    def main(self):
        parser = argparse.ArgumentParser(
            description="Comprehensive Python Performance Monitor"
        )

        subparsers = parser.add_subparsers(dest='mode', help='Monitoring mode')

        # Development mode (Scalene)
        dev_parser = subparsers.add_parser('dev', help='Development profiling with Scalene')
        dev_parser.add_argument('script', help='Python script to profile')
        dev_parser.add_argument('--reduced', action='store_true', help='Reduced profile')
        dev_parser.add_argument('--html', action='store_true', help='HTML output')
        dev_parser.add_argument('--cpu-only', action='store_true', help='CPU only')

        # Production mode (py-spy + psutil)
        prod_parser = subparsers.add_parser('prod', help='Production monitoring')
        prod_parser.add_argument('--pid', type=int, help='Process ID to monitor')
        prod_parser.add_argument('--duration', type=int, default=60, help='Duration in seconds')
        prod_parser.add_argument('--pyspy', action='store_true', help='Include py-spy profiling')

        # Comprehensive mode
        comp_parser = subparsers.add_parser('comprehensive', help='Full monitoring suite')
        comp_parser.add_argument('--pid', type=int, help='Process ID to monitor')
        comp_parser.add_argument('--duration', type=int, default=60, help='Duration in seconds')

        args = parser.parse_args()

        if args.mode == 'dev':
            options = []
            if args.reduced:
                options.append('reduced')
            if args.html:
                options.append('html')
            if args.cpu_only:
                options.append('cpu_only')

            self.run_scalene_development(args.script, options)

        elif args.mode == 'prod':
            if args.pyspy:
                self.run_pyspy_production(args.pid, args.duration)
            else:
                monitor = EnhancedProductionMonitor(args.pid)
                monitor.start_monitoring(interval=1, duration=args.duration)
                monitor.save_report()

        elif args.mode == 'comprehensive':
            self.run_comprehensive_monitoring(args.pid, args.duration)

        else:
            parser.print_help()

if __name__ == "__main__":
    monitor = ComprehensiveMonitor()
    monitor.main()
```

## 6. Sử Dụng Thực Tế

### 6.1 Development Workflow

```bash
# 1. Profile ứng dụng development
python monitor.py dev your_app.py --html --reduced

# 2. Profile specific functions
# Thêm @profile decorator vào functions cần profile
scalene your_app.py

# 3. Profile với options khác nhau
scalene --cpu-only your_app.py                    # Chỉ CPU
scalene --profile-interval 5.0 your_app.py        # Profile mỗi 5s
scalene --reduced-profile your_app.py             # Chỉ lines quan trọng
```

### 6.2 Production Monitoring

```bash
# 1. Monitor process hiện tại
python monitor.py comprehensive --duration 300

# 2. Monitor specific process
python monitor.py comprehensive --pid 12345 --duration 600

# 3. Chỉ py-spy profiling
python monitor.py prod --pid 12345 --pyspy --duration 120

# 4. Real-time monitoring
python monitor.py prod --pid 12345 --duration 0  # Unlimited
```

### 6.3 Tích Hợp Vào Ứng Dụng

```python
# Trong ứng dụng chính
import os
from performance_monitor import EnhancedProductionMonitor

class Application:
    def __init__(self):
        self.monitor = None
        if os.getenv('ENABLE_MONITORING', 'false').lower() == 'true':
            self.monitor = EnhancedProductionMonitor()
            self.start_background_monitoring()

    def start_background_monitoring(self):
        """Start monitoring in background thread"""
        import threading

        def monitor_worker():
            self.monitor.start_monitoring(interval=5, duration=None)

        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
        print("🔍 Background monitoring started")

    def get_current_metrics(self):
        """Get current performance metrics"""
        if self.monitor:
            return self.monitor.get_resource_usage()
        return None

# Sử dụng
app = Application()

# Trong environment variables:
# export ENABLE_MONITORING=true
```

## 7. Kết Luận

### 7.1 Overhead Comparison

| Tool | Overhead | Use Case | Features |
|------|----------|----------|----------|
| **Scalene** | 10-20% | Development | CPU+GPU+Memory, Line-level, AI suggestions |
| **py-spy** | <1% | Production | CPU profiling, Flame graphs |
| **psutil** | <1% | Production | System monitoring, Real-time |
| **memory_profiler** | 10-50x | Development | Memory line-by-line |
| **line_profiler** | 10-100x | Development | CPU line-by-line |

### 7.2 Khuyến Nghị

1. **Development**: Sử dụng **Scalene** với `--reduced-profile` cho overhead thấp
2. **Production**: Sử dụng **py-spy + psutil** cho monitoring liên tục
3. **Debugging**: Sử dụng **Scalene** với full features
4. **CI/CD**: Tích hợp **py-spy** cho performance regression testing

### 7.3 Best Practices

- ✅ Luôn test performance impact trước khi deploy monitoring
- ✅ Sử dụng sampling thay vì continuous profiling trong production
- ✅ Monitor cả system-level và application-level metrics
- ✅ Set up alerts cho bottlenecks và UI blocking
- ✅ Regular review performance reports và optimize

## 8. Troubleshooting Cross-Platform Issues

### 8.1 Windows Specific Issues

```bash
# Nếu py-spy không work với permission
# Chạy Command Prompt as Administrator
py-spy record -o profile.svg --pid 12345

# Nếu psutil báo lỗi access denied
# Chạy Python as Administrator hoặc sử dụng:
import psutil
process = psutil.Process()
try:
    info = process.memory_info()
except psutil.AccessDenied:
    print("Need administrator privileges")
```

### 8.2 macOS Specific Issues

```bash
# Nếu py-spy không work do SIP (System Integrity Protection)
# Option 1: Disable SIP (not recommended for production)
# Restart -> Hold Cmd+R -> Terminal -> csrutil disable

# Option 2: Use alternative approach
# Sử dụng chỉ psutil cho production monitoring
python -c "
import psutil
p = psutil.Process()
print(f'CPU: {p.cpu_percent()}%, Memory: {p.memory_percent()}%')
"
```

### 8.3 Linux Specific Issues

```bash
# Nếu py-spy cần sudo
sudo py-spy record -o profile.svg --pid 12345

# Hoặc add user vào group để không cần sudo
sudo usermod -a -G perf $USER
# Logout và login lại

# Nếu không có quyền sudo, chỉ dùng psutil
python monitor.py prod --pid 12345  # Không dùng py-spy
```

### 8.4 Safe Installation Script

```python
#!/usr/bin/env python3
"""
Safe cross-platform installation checker
"""

import subprocess
import sys
import platform

def check_and_install():
    """Check và install packages an toàn"""

    print(f"🖥️ Platform: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")

    # Core packages (always work)
    core_packages = ['psutil']

    # Optional packages
    optional_packages = {
        'py-spy': 'Advanced CPU profiling',
        'pynvml': 'NVIDIA GPU monitoring (requires NVIDIA GPU)',
        'scalene': 'Comprehensive profiling (development)'
    }

    # Install core packages
    for package in core_packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False

    # Install optional packages
    for package, description in optional_packages.items():
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully - {description}")
        except subprocess.CalledProcessError:
            print(f"⚠️ {package} installation failed - {description}")
            print(f"   This is OK if you don't need this feature")

    # Test imports
    print("\n🧪 Testing imports...")

    # Test psutil (required)
    try:
        import psutil
        print(f"✅ psutil working - CPU: {psutil.cpu_percent()}%")
    except ImportError:
        print("❌ psutil not working - this is required!")
        return False

    # Test py-spy (optional)
    try:
        result = subprocess.run(['py-spy', '--help'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ py-spy working")
        else:
            print("⚠️ py-spy installed but may need admin privileges")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️ py-spy not available")

    # Test pynvml (optional)
    try:
        import pynvml
        pynvml.nvmlInit()
        gpu_count = pynvml.nvmlDeviceGetCount()
        print(f"✅ pynvml working - {gpu_count} GPU(s) detected")
    except Exception as e:
        print(f"⚠️ pynvml not working: {e}")
        print("   This is normal if you don't have NVIDIA GPU")

    # Test scalene (optional)
    try:
        result = subprocess.run(['scalene', '--help'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ scalene working")
        else:
            print("⚠️ scalene installed but may have issues")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️ scalene not available")

    print("\n🎉 Setup completed! You can now use the monitoring tools.")
    return True

if __name__ == "__main__":
    check_and_install()
```

### 8.5 Minimal Fallback Monitor (100% Cross-Platform)

```python
#!/usr/bin/env python3
"""
Minimal performance monitor - works everywhere
Chỉ dùng psutil (guaranteed cross-platform)
"""

import psutil
import time
import json
from datetime import datetime

class MinimalMonitor:
    def __init__(self, pid=None):
        self.process = psutil.Process(pid) if pid else psutil.Process()

    def get_basic_metrics(self):
        """Metrics cơ bản - works on all platforms"""
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': self.process.cpu_percent(),
            'memory_mb': self.process.memory_info().rss / 1024 / 1024,
            'memory_percent': self.process.memory_percent(),
            'threads': self.process.num_threads(),
            'system_cpu': psutil.cpu_percent(),
            'system_memory': psutil.virtual_memory().percent
        }

    def monitor(self, duration=60, interval=1):
        """Simple monitoring loop"""
        print("🔍 Starting minimal cross-platform monitoring...")

        for i in range(duration):
            metrics = self.get_basic_metrics()

            print(f"[{i+1:3d}/{duration}] "
                  f"CPU: {metrics['cpu_percent']:5.1f}% | "
                  f"RAM: {metrics['memory_mb']:6.1f}MB | "
                  f"Threads: {metrics['threads']:2d}")

            time.sleep(interval)

        print("✅ Monitoring completed")

if __name__ == "__main__":
    monitor = MinimalMonitor()
    monitor.monitor(duration=30)
```

---

## 🎯 **Kết Luận về Cross-Platform**

### ✅ **Hoàn toàn an toàn và cross-platform:**
- **psutil**: Works everywhere, không cần setup gì
- **Minimal monitor script**: 100% guaranteed work

### ⚠️ **Có thể cần setup nhẹ:**
- **py-spy**: Works nhưng có thể cần admin privileges
- **scalene**: Generally works, có thể cần compiler tools

### 🎯 **Hardware dependent:**
- **pynvml**: Chỉ work khi có NVIDIA GPU

### 💡 **Khuyến nghị:**
1. **Bắt đầu với psutil** - guaranteed work everywhere
2. **Thêm py-spy** nếu cần advanced profiling
3. **Thêm pynvml** chỉ khi có NVIDIA GPU
4. **Luôn có fallback** cho trường hợp packages không work

## 9. 🚨 PyInstaller Compatibility & Packaging Issues

### 9.1 Compatibility Matrix

| Package | PyInstaller Support | Issues | Solutions |
|---------|-------------------|---------|-----------|
| **psutil** | ✅ Excellent | None | Works out of the box |
| **py-spy** | ❌ **KHÔNG WORK** | Binary executable | Use alternative approach |
| **pynvml** | ⚠️ Problematic | NVIDIA driver dependencies | Conditional import |
| **scalene** | ❌ **KHÔNG WORK** | Complex C extensions | Use alternative approach |

### 9.2 ❌ **Packages KHÔNG thể đóng gói với PyInstaller:**

#### **py-spy** - KHÔNG WORK
```python
# ❌ py-spy là binary executable riêng biệt
# Không thể embed vào PyInstaller executable
# Lý do: py-spy không phải Python package thuần túy
```

#### **scalene** - KHÔNG WORK
```python
# ❌ scalene có nhiều C extensions phức tạp
# Có thể gây lỗi khi đóng gói
# Lý do: Complex memory allocator và native code
```

### 9.3 ✅ **Safe PyInstaller-Compatible Solution**

```python
#!/usr/bin/env python3
"""
PyInstaller-Safe Performance Monitor
Chỉ sử dụng packages tương thích 100% với PyInstaller
"""

import psutil
import time
import json
import threading
import sys
import os
from datetime import datetime
from collections import defaultdict

class PyInstallerSafeMonitor:
    """
    Performance monitor an toàn cho PyInstaller
    Chỉ dùng psutil + built-in libraries
    """

    def __init__(self, target_pid=None):
        self.target_pid = target_pid or os.getpid()
        self.process = psutil.Process(self.target_pid)
        self.monitoring = False
        self.metrics_history = []

        # GPU support (conditional)
        self.gpu_available = self._check_gpu_support()

    def _check_gpu_support(self):
        """Kiểm tra GPU support một cách an toàn"""
        try:
            # Conditional import để tránh lỗi khi package không có
            import pynvml
            pynvml.nvmlInit()
            gpu_count = pynvml.nvmlDeviceGetCount()
            print(f"✅ GPU Support: {gpu_count} NVIDIA GPU(s) detected")
            return True
        except Exception as e:
            print(f"⚠️ GPU Support: Not available ({str(e)})")
            return False

    def get_comprehensive_metrics(self):
        """Lấy metrics toàn diện chỉ với psutil"""
        try:
            # Process metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()

            # System metrics
            system_cpu = psutil.cpu_percent(percpu=True)
            system_memory = psutil.virtual_memory()
            system_disk = psutil.disk_usage('/')

            # Network metrics
            net_io = psutil.net_io_counters()

            # Process details
            try:
                io_counters = self.process.io_counters()
                connections = len(self.process.connections())
                open_files = len(self.process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                io_counters = None
                connections = 0
                open_files = 0

            metrics = {
                'timestamp': datetime.now().isoformat(),
                'process': {
                    'pid': self.target_pid,
                    'name': self.process.name(),
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_info.rss / 1024 / 1024,
                    'memory_percent': memory_percent,
                    'threads_count': self.process.num_threads(),
                    'connections': connections,
                    'open_files': open_files,
                    'status': self.process.status(),
                },
                'system': {
                    'cpu_per_core': system_cpu,
                    'cpu_avg': sum(system_cpu) / len(system_cpu),
                    'memory_percent': system_memory.percent,
                    'memory_total_gb': system_memory.total / 1024 / 1024 / 1024,
                    'memory_available_gb': system_memory.available / 1024 / 1024 / 1024,
                    'disk_percent': system_disk.percent,
                    'disk_free_gb': system_disk.free / 1024 / 1024 / 1024,
                },
                'network': {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv,
                }
            }

            # Add I/O if available
            if io_counters:
                metrics['io'] = {
                    'read_bytes': io_counters.read_bytes,
                    'write_bytes': io_counters.write_bytes,
                    'read_count': io_counters.read_count,
                    'write_count': io_counters.write_count,
                }

            # Add GPU if available
            if self.gpu_available:
                metrics['gpu'] = self._get_gpu_metrics()

            return metrics

        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _get_gpu_metrics(self):
        """Lấy GPU metrics nếu có"""
        try:
            import pynvml
            gpu_metrics = []

            for i in range(pynvml.nvmlDeviceGetCount()):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)

                gpu_metrics.append({
                    'gpu_id': i,
                    'name': pynvml.nvmlDeviceGetName(handle).decode('utf-8'),
                    'utilization': util.gpu,
                    'memory_percent': (mem_info.used / mem_info.total) * 100,
                    'memory_used_mb': mem_info.used / 1024 / 1024,
                    'temperature': temp
                })

            return gpu_metrics
        except:
            return []

    def detect_performance_issues(self, metrics):
        """Phát hiện vấn đề performance"""
        issues = []

        process = metrics.get('process', {})
        system = metrics.get('system', {})

        # High CPU usage
        if process.get('cpu_percent', 0) > 80:
            issues.append({
                'type': 'HIGH_CPU',
                'severity': 'HIGH',
                'message': f"Process CPU: {process['cpu_percent']:.1f}%",
                'suggestion': 'Optimize CPU-intensive operations'
            })

        # High memory usage
        if process.get('memory_percent', 0) > 70:
            issues.append({
                'type': 'HIGH_MEMORY',
                'severity': 'HIGH',
                'message': f"Process Memory: {process['memory_percent']:.1f}%",
                'suggestion': 'Check for memory leaks'
            })

        # System resource issues
        if system.get('memory_percent', 0) > 85:
            issues.append({
                'type': 'SYSTEM_MEMORY_LOW',
                'severity': 'CRITICAL',
                'message': f"System Memory: {system['memory_percent']:.1f}%",
                'suggestion': 'System running low on memory'
            })

        # Too many threads
        if process.get('threads_count', 0) > 50:
            issues.append({
                'type': 'HIGH_THREAD_COUNT',
                'severity': 'MEDIUM',
                'message': f"Thread count: {process['threads_count']}",
                'suggestion': 'Consider using thread pools'
            })

        return issues

    def start_monitoring(self, interval=1, duration=None, output_file=None):
        """Bắt đầu monitoring"""
        self.monitoring = True
        start_time = time.time()

        print(f"🔍 PyInstaller-Safe Monitoring Started")
        print(f"📊 PID: {self.target_pid} | Interval: {interval}s")
        print(f"🎯 GPU Support: {'Yes' if self.gpu_available else 'No'}")
        print("-" * 60)

        try:
            while self.monitoring:
                if duration and (time.time() - start_time) > duration:
                    break

                metrics = self.get_comprehensive_metrics()
                self.metrics_history.append(metrics)

                # Detect issues
                issues = self.detect_performance_issues(metrics)

                # Display real-time stats
                self._display_stats(metrics, issues)

                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n⏹️ Monitoring stopped by user")
        finally:
            self.monitoring = False

            # Save report if requested
            if output_file:
                self.save_report(output_file)

    def _display_stats(self, metrics, issues):
        """Hiển thị stats real-time"""
        # Clear screen
        os.system('cls' if os.name == 'nt' else 'clear')

        print("=" * 70)
        print(f"🔍 PYINSTALLER-SAFE PERFORMANCE MONITOR")
        print(f"⏰ {metrics.get('timestamp', 'N/A')}")
        print("=" * 70)

        # Process info
        process = metrics.get('process', {})
        print(f"📊 PROCESS: {process.get('name', 'Unknown')} (PID: {process.get('pid', 'N/A')})")
        print(f"   CPU: {process.get('cpu_percent', 0):.1f}%")
        print(f"   Memory: {process.get('memory_mb', 0):.1f} MB ({process.get('memory_percent', 0):.1f}%)")
        print(f"   Threads: {process.get('threads_count', 0)}")
        print(f"   Status: {process.get('status', 'Unknown')}")

        # System info
        system = metrics.get('system', {})
        print(f"\n🖥️ SYSTEM:")
        print(f"   CPU: {system.get('cpu_avg', 0):.1f}%")
        print(f"   Memory: {system.get('memory_percent', 0):.1f}% "
              f"({system.get('memory_available_gb', 0):.1f}GB free)")
        print(f"   Disk: {system.get('disk_percent', 0):.1f}% "
              f"({system.get('disk_free_gb', 0):.1f}GB free)")

        # GPU info
        gpu_metrics = metrics.get('gpu', [])
        if gpu_metrics:
            print(f"\n🎮 GPU:")
            for gpu in gpu_metrics:
                print(f"   GPU {gpu['gpu_id']}: {gpu['utilization']}% "
                      f"| Mem: {gpu['memory_percent']:.1f}% "
                      f"| Temp: {gpu['temperature']}°C")

        # Issues
        if issues:
            print(f"\n⚠️ PERFORMANCE ISSUES:")
            for issue in issues:
                severity_icon = {"CRITICAL": "🔴", "HIGH": "🟡", "MEDIUM": "🟢"}.get(issue['severity'], "ℹ️")
                print(f"   {severity_icon} {issue['message']}")
                print(f"      💡 {issue['suggestion']}")

        print("\n" + "=" * 70)
        print("Press Ctrl+C to stop monitoring")

    def save_report(self, filename="performance_report.json"):
        """Lưu báo cáo"""
        report = {
            'summary': {
                'total_samples': len(self.metrics_history),
                'target_pid': self.target_pid,
                'gpu_support': self.gpu_available,
                'generated_at': datetime.now().isoformat()
            },
            'metrics': self.metrics_history
        }

        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)

        print(f"📄 Report saved to {filename}")

def main():
    """Main function cho PyInstaller executable"""
    import argparse

    parser = argparse.ArgumentParser(description="PyInstaller-Safe Performance Monitor")
    parser.add_argument('--pid', type=int, help='Process ID to monitor')
    parser.add_argument('--duration', type=int, default=60, help='Duration in seconds')
    parser.add_argument('--interval', type=int, default=1, help='Sampling interval')
    parser.add_argument('--output', help='Output file for report')

    args = parser.parse_args()

    monitor = PyInstallerSafeMonitor(args.pid)
    monitor.start_monitoring(
        interval=args.interval,
        duration=args.duration,
        output_file=args.output
    )

if __name__ == "__main__":
    main()
```

### 9.4 PyInstaller Build Script

```python
# build_monitor.py
"""
Script để build PyInstaller executable
"""

import subprocess
import sys
import os

def build_executable():
    """Build performance monitor executable"""

    script_name = "pyinstaller_safe_monitor.py"

    # PyInstaller command
    cmd = [
        'pyinstaller',
        '--onefile',                    # Single executable
        '--name=PerformanceMonitor',    # Executable name
        '--console',                    # Console app
        '--clean',                      # Clean build
        script_name
    ]

    # Add hidden imports nếu cần
    hidden_imports = [
        'psutil',
        'psutil._psutil_windows',  # Windows specific
        'psutil._psutil_posix',    # Linux/macOS specific
    ]

    for import_name in hidden_imports:
        cmd.extend(['--hidden-import', import_name])

    # Optional: Add pynvml nếu có GPU
    try:
        import pynvml
        cmd.extend(['--hidden-import', 'pynvml'])
        print("✅ Adding pynvml support")
    except ImportError:
        print("⚠️ pynvml not available, skipping GPU support")

    print("🔨 Building executable...")
    print(f"Command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Build successful!")
        print("📁 Executable location: dist/PerformanceMonitor.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

if __name__ == "__main__":
    build_executable()
```

### 9.5 Usage Instructions

```bash
# 1. Tạo file pyinstaller_safe_monitor.py với code ở trên
# 2. Build executable
python build_monitor.py

# 3. Sử dụng executable
./dist/PerformanceMonitor.exe --duration 60 --interval 1
./dist/PerformanceMonitor.exe --pid 1234 --output report.json
```

### 9.6 ⚠️ **Lưu Ý Quan Trọng về PyInstaller**

#### **Packages KHÔNG thể đóng gói:**
- ❌ **py-spy**: Binary executable riêng biệt
- ❌ **scalene**: Complex C extensions
- ❌ **line_profiler**: Cython dependencies
- ❌ **memory_profiler**: External dependencies

#### **Packages an toàn cho PyInstaller:**
- ✅ **psutil**: Works perfectly
- ⚠️ **pynvml**: Works nếu có NVIDIA drivers trên target machine

### 9.7 Alternative Approach cho Distributed Applications

```python
# distributed_monitor.py
"""
Alternative: Tách monitoring thành separate service
"""

import socket
import json
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler

class MonitoringServer:
    """
    HTTP server để expose monitoring data
    Có thể chạy riêng biệt với main application
    """

    def __init__(self, port=8888):
        self.port = port
        self.monitor = PyInstallerSafeMonitor()

    def start_server(self):
        """Start HTTP monitoring server"""

        class MonitorHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/metrics':
                    metrics = self.server.monitor.get_comprehensive_metrics()

                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    self.wfile.write(json.dumps(metrics, indent=2).encode())
                else:
                    self.send_response(404)
                    self.end_headers()

        server = HTTPServer(('localhost', self.port), MonitorHandler)
        server.monitor = self.monitor

        print(f"🌐 Monitoring server started at http://localhost:{self.port}/metrics")
        server.serve_forever()

# Sử dụng:
# 1. Main app chạy monitoring server trong background thread
# 2. External tools có thể query metrics qua HTTP
# 3. Không cần đóng gói monitoring tools vào main executable
```

### 9.8 Recommended Architecture cho Production

```
📁 Production Deployment
├── 📄 main_app.exe              # Main application (PyInstaller)
├── 📄 monitor_service.exe       # Monitoring service (PyInstaller-safe)
├── 📁 monitoring_tools/         # External tools (không đóng gói)
│   ├── py-spy.exe              # Download riêng
│   ├── scalene/                # Install riêng
│   └── run_advanced_profiling.bat
└── 📄 config.json              # Configuration
```

### 9.9 Final Recommendations

#### **Cho PyInstaller Deployment:**
1. ✅ **Chỉ dùng psutil** trong main executable
2. ✅ **pynvml conditional import** nếu cần GPU
3. ❌ **Tránh py-spy, scalene** trong executable
4. ✅ **Tách monitoring thành separate service** nếu cần advanced features

#### **Cho Development:**
- Dùng full toolset (scalene, py-spy, etc.)
- Không cần lo PyInstaller compatibility

#### **Cho Production:**
- Main app: PyInstaller-safe monitoring
- Advanced profiling: External tools chạy riêng

---

## 🎯 **Kết Luận về PyInstaller Compatibility**

### ✅ **An toàn 100%:**
- **psutil**: Perfect compatibility
- **Built-in libraries**: threading, json, time, etc.

### ⚠️ **Có điều kiện:**
- **pynvml**: Cần NVIDIA drivers trên target machine

### ❌ **Không thể đóng gói:**
- **py-spy**: Binary executable
- **scalene**: Complex dependencies
- **line_profiler**: Cython issues
- **memory_profiler**: External dependencies

**Solution**: Sử dụng PyInstaller-safe monitor cho main app, external tools cho advanced profiling.

---

## 10. 🎥 Camera Threading Performance Management

### 10.1 Vấn Đề Camera Loading Performance

Trong hệ thống camera iVMS, việc khởi tạo và load nhiều camera cùng lúc có thể gây ra:

- ⚠️ **Resource Overload**: Quá nhiều threads decode video cùng lúc
- ⚠️ **App Crash/Not Responding**: Khi kéo nhóm camera vào grid
- ⚠️ **Bất ổn tài nguyên**: Thời gian load camera không đồng đều
- ⚠️ **UI Blocking**: Main thread bị block khi khởi tạo PyAV/VLC

### 10.2 Camera System Architecture Analysis

<augment_code_snippet path="src/common/camera/video_player_manager.py" mode="EXCERPT">
````python
class VideoPlayerManager(QObject):
    def __init__(self, parent=None, target: Callable = None, args=()):
        super().__init__(parent)
        self._cleanup_lock = threading.Lock()
        self.list_player = {}
        self.list_widgets = {}
        threadPoolManager.create_pool("VideoPlayerManager",max_threads=64)  # 🚨 64 threads!

    def register_player(self,widget:QWidget = None,camera_model:CameraModel = None,stream_type = CommonEnum.StreamType.MAIN_STREAM):
        threadPoolManager.run("VideoPlayerManager",self.process_player,args=(widget,camera_model,stream_type,))
````
</augment_code_snippet>

<augment_code_snippet path="src/common/camera/live_stream_player.py" mode="EXCERPT">
````python
def connect_camera_stream(self):
    try:
        # Initialize PyAV wrapper with hardware acceleration enabled
        self.pyav_wrapper = PyAVWrapper()  # 🚨 Blocking operation

        if not self.pyav_wrapper.open_stream(self.stream_link):  # 🚨 Network I/O blocking
            logger.debug(f'Camera {self.camera_id}: Failed to open stream')
            return False
````
</augment_code_snippet>

### 10.3 Camera Threading Performance Monitor

```python
#!/usr/bin/env python3
"""
Camera Threading Performance Monitor
Specialized monitor cho camera loading performance
"""

import psutil
import threading
import time
import json
from datetime import datetime
from collections import defaultdict, deque
import queue

class CameraThreadingMonitor:
    """
    Monitor chuyên biệt cho camera threading performance
    """

    def __init__(self, target_pid=None):
        self.target_pid = target_pid or os.getpid()
        self.process = psutil.Process(self.target_pid)
        self.monitoring = False

        # Camera-specific metrics
        self.camera_metrics = defaultdict(dict)
        self.thread_metrics = defaultdict(lambda: deque(maxlen=100))
        self.resource_timeline = deque(maxlen=1000)

        # Performance thresholds
        self.thresholds = {
            'max_concurrent_cameras': 16,  # Max cameras loading cùng lúc
            'max_cpu_per_camera': 15,      # Max CPU % per camera
            'max_memory_per_camera': 100,  # Max MB per camera
            'max_thread_count': 100,       # Max total threads
            'camera_load_timeout': 30,     # Timeout cho camera load (seconds)
        }

        # Camera state tracking
        self.camera_states = {}  # camera_id -> state info
        self.loading_cameras = set()  # cameras đang loading
        self.failed_cameras = set()   # cameras failed to load

    def track_camera_loading(self, camera_id, state, additional_info=None):
        """Track camera loading state changes"""
        timestamp = datetime.now()

        self.camera_states[camera_id] = {
            'state': state,
            'timestamp': timestamp,
            'info': additional_info or {}
        }

        if state == 'loading':
            self.loading_cameras.add(camera_id)
        elif state in ['loaded', 'failed']:
            self.loading_cameras.discard(camera_id)
            if state == 'failed':
                self.failed_cameras.add(camera_id)

    def get_camera_performance_metrics(self):
        """Lấy metrics performance cho camera system"""
        try:
            # Basic process metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            thread_count = self.process.num_threads()

            # Thread analysis
            threads = self.process.threads()
            thread_analysis = self._analyze_threads(threads)

            # Camera-specific analysis
            camera_analysis = self._analyze_camera_performance()

            # Resource pressure analysis
            pressure_analysis = self._analyze_resource_pressure()

            metrics = {
                'timestamp': datetime.now().isoformat(),
                'process': {
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_info.rss / 1024 / 1024,
                    'thread_count': thread_count,
                },
                'threads': thread_analysis,
                'cameras': camera_analysis,
                'pressure': pressure_analysis,
                'thresholds': self.thresholds
            }

            # Store in timeline
            self.resource_timeline.append(metrics)

            return metrics

        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _analyze_threads(self, threads):
        """Phân tích thread performance"""
        total_threads = len(threads)

        # Categorize threads by CPU usage
        high_cpu_threads = []
        for thread in threads:
            total_time = thread.user_time + thread.system_time
            if total_time > 1.0:  # Threads with >1s CPU time
                high_cpu_threads.append({
                    'thread_id': thread.id,
                    'cpu_time': total_time,
                    'user_time': thread.user_time,
                    'system_time': thread.system_time
                })

        # Sort by CPU usage
        high_cpu_threads.sort(key=lambda x: x['cpu_time'], reverse=True)

        return {
            'total_count': total_threads,
            'high_cpu_threads': high_cpu_threads[:10],  # Top 10
            'thread_pressure': 'HIGH' if total_threads > self.thresholds['max_thread_count'] else 'NORMAL'
        }

    def _analyze_camera_performance(self):
        """Phân tích performance của camera system"""
        current_time = datetime.now()

        # Analyze loading cameras
        loading_analysis = {
            'count': len(self.loading_cameras),
            'cameras': list(self.loading_cameras),
            'overload': len(self.loading_cameras) > self.thresholds['max_concurrent_cameras']
        }

        # Analyze camera load times
        load_times = []
        stuck_cameras = []

        for camera_id, state_info in self.camera_states.items():
            if state_info['state'] == 'loading':
                load_duration = (current_time - state_info['timestamp']).total_seconds()
                load_times.append(load_duration)

                if load_duration > self.thresholds['camera_load_timeout']:
                    stuck_cameras.append({
                        'camera_id': camera_id,
                        'load_duration': load_duration,
                        'info': state_info['info']
                    })

        return {
            'loading': loading_analysis,
            'stuck_cameras': stuck_cameras,
            'failed_count': len(self.failed_cameras),
            'avg_load_time': sum(load_times) / len(load_times) if load_times else 0,
            'max_load_time': max(load_times) if load_times else 0
        }

    def _analyze_resource_pressure(self):
        """Phân tích áp lực tài nguyên"""
        if len(self.resource_timeline) < 2:
            return {'status': 'INSUFFICIENT_DATA'}

        recent_metrics = list(self.resource_timeline)[-10:]  # Last 10 samples

        # CPU trend analysis
        cpu_values = [m['process']['cpu_percent'] for m in recent_metrics]
        cpu_trend = 'INCREASING' if cpu_values[-1] > cpu_values[0] else 'STABLE'

        # Memory trend analysis
        memory_values = [m['process']['memory_mb'] for m in recent_metrics]
        memory_trend = 'INCREASING' if memory_values[-1] > memory_values[0] else 'STABLE'

        # Thread count trend
        thread_values = [m['process']['thread_count'] for m in recent_metrics]
        thread_trend = 'INCREASING' if thread_values[-1] > thread_values[0] else 'STABLE'

        # Overall pressure assessment
        current_cpu = cpu_values[-1]
        current_memory = memory_values[-1]
        current_threads = thread_values[-1]

        pressure_level = 'LOW'
        if (current_cpu > 70 or current_memory > 1000 or current_threads > 80):
            pressure_level = 'HIGH'
        elif (current_cpu > 50 or current_memory > 500 or current_threads > 50):
            pressure_level = 'MEDIUM'

        return {
            'status': 'ANALYZED',
            'pressure_level': pressure_level,
            'trends': {
                'cpu': cpu_trend,
                'memory': memory_trend,
                'threads': thread_trend
            },
            'current': {
                'cpu': current_cpu,
                'memory': current_memory,
                'threads': current_threads
            }
        }

    def detect_camera_issues(self, metrics):
        """Phát hiện vấn đề camera performance"""
        issues = []

        cameras = metrics.get('cameras', {})
        pressure = metrics.get('pressure', {})
        threads = metrics.get('threads', {})

        # Too many cameras loading simultaneously
        if cameras.get('loading', {}).get('overload', False):
            issues.append({
                'type': 'CAMERA_OVERLOAD',
                'severity': 'HIGH',
                'message': f"Too many cameras loading: {cameras['loading']['count']}/{self.thresholds['max_concurrent_cameras']}",
                'suggestion': 'Implement camera loading queue/throttling'
            })

        # Stuck cameras
        stuck_cameras = cameras.get('stuck_cameras', [])
        if stuck_cameras:
            issues.append({
                'type': 'STUCK_CAMERAS',
                'severity': 'HIGH',
                'message': f"{len(stuck_cameras)} cameras stuck loading",
                'suggestion': 'Check network connectivity and stream URLs',
                'details': stuck_cameras
            })

        # High resource pressure
        if pressure.get('pressure_level') == 'HIGH':
            issues.append({
                'type': 'RESOURCE_PRESSURE',
                'severity': 'CRITICAL',
                'message': f"High resource pressure detected",
                'suggestion': 'Reduce concurrent camera operations',
                'trends': pressure.get('trends', {})
            })

        # Thread explosion
        if threads.get('thread_pressure') == 'HIGH':
            issues.append({
                'type': 'THREAD_EXPLOSION',
                'severity': 'HIGH',
                'message': f"Thread count too high: {threads['total_count']}",
                'suggestion': 'Implement thread pooling and limits'
            })

        return issues

    def start_monitoring(self, interval=2, duration=None):
        """Bắt đầu camera threading monitoring"""
        self.monitoring = True
        start_time = time.time()

        print(f"🎥 Camera Threading Monitor Started")
        print(f"📊 PID: {self.target_pid} | Interval: {interval}s")
        print("-" * 70)

        try:
            while self.monitoring:
                if duration and (time.time() - start_time) > duration:
                    break

                metrics = self.get_camera_performance_metrics()
                issues = self.detect_camera_issues(metrics)

                self._display_camera_stats(metrics, issues)

                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n⏹️ Camera monitoring stopped by user")
        finally:
            self.monitoring = False

    def _display_camera_stats(self, metrics, issues):
        """Hiển thị camera performance stats"""
        os.system('cls' if os.name == 'nt' else 'clear')

        print("=" * 80)
        print(f"🎥 CAMERA THREADING PERFORMANCE MONITOR")
        print(f"⏰ {metrics.get('timestamp', 'N/A')}")
        print("=" * 80)

        # Process stats
        process = metrics.get('process', {})
        print(f"📊 PROCESS STATS:")
        print(f"   CPU: {process.get('cpu_percent', 0):.1f}%")
        print(f"   Memory: {process.get('memory_mb', 0):.1f} MB")
        print(f"   Threads: {process.get('thread_count', 0)}")

        # Camera stats
        cameras = metrics.get('cameras', {})
        loading = cameras.get('loading', {})
        print(f"\n🎥 CAMERA STATS:")
        print(f"   Loading: {loading.get('count', 0)}/{self.thresholds['max_concurrent_cameras']} cameras")
        print(f"   Failed: {cameras.get('failed_count', 0)} cameras")
        print(f"   Avg Load Time: {cameras.get('avg_load_time', 0):.1f}s")
        print(f"   Max Load Time: {cameras.get('max_load_time', 0):.1f}s")

        # Thread analysis
        threads = metrics.get('threads', {})
        print(f"\n🧵 THREAD ANALYSIS:")
        print(f"   Total Threads: {threads.get('total_count', 0)}")
        print(f"   Pressure: {threads.get('thread_pressure', 'UNKNOWN')}")

        high_cpu_threads = threads.get('high_cpu_threads', [])[:3]
        if high_cpu_threads:
            print(f"   Top CPU Threads:")
            for i, thread in enumerate(high_cpu_threads, 1):
                print(f"     {i}. Thread {thread['thread_id']}: {thread['cpu_time']:.2f}s")

        # Resource pressure
        pressure = metrics.get('pressure', {})
        if pressure.get('status') == 'ANALYZED':
            print(f"\n📈 RESOURCE PRESSURE:")
            print(f"   Level: {pressure.get('pressure_level', 'UNKNOWN')}")
            trends = pressure.get('trends', {})
            print(f"   CPU Trend: {trends.get('cpu', 'UNKNOWN')}")
            print(f"   Memory Trend: {trends.get('memory', 'UNKNOWN')}")
            print(f"   Thread Trend: {trends.get('threads', 'UNKNOWN')}")

        # Issues
        if issues:
            print(f"\n⚠️ CAMERA PERFORMANCE ISSUES:")
            for issue in issues:
                severity_icon = {"CRITICAL": "🔴", "HIGH": "🟡", "MEDIUM": "🟢"}.get(issue['severity'], "ℹ️")
                print(f"   {severity_icon} {issue['message']}")
                print(f"      💡 {issue['suggestion']}")

        print("\n" + "=" * 80)
        print("Press Ctrl+C to stop monitoring")
```

### 10.4 Camera Loading Queue Manager

```python
#!/usr/bin/env python3
"""
Camera Loading Queue Manager
Quản lý việc load camera theo queue để tránh overload
"""

import threading
import time
import queue
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Callable

class CameraLoadState(Enum):
    QUEUED = "queued"
    LOADING = "loading"
    LOADED = "loaded"
    FAILED = "failed"
    TIMEOUT = "timeout"

@dataclass
class CameraLoadRequest:
    camera_id: str
    camera_model: object
    stream_type: str
    priority: int = 0  # Higher number = higher priority
    callback: Optional[Callable] = None
    timeout: float = 30.0
    retry_count: int = 0
    max_retries: int = 3

class CameraLoadingQueueManager:
    """
    Quản lý queue loading camera để tránh overload tài nguyên
    """

    def __init__(self, max_concurrent_loads=4, max_queue_size=50):
        self.max_concurrent_loads = max_concurrent_loads
        self.max_queue_size = max_queue_size

        # Queues
        self.load_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.retry_queue = queue.Queue()

        # State tracking
        self.loading_cameras = {}  # camera_id -> CameraLoadRequest
        self.completed_cameras = {}  # camera_id -> (state, timestamp)

        # Threading
        self.worker_threads = []
        self.running = False
        self.stats_lock = threading.Lock()

        # Statistics
        self.stats = {
            'total_requests': 0,
            'successful_loads': 0,
            'failed_loads': 0,
            'timeout_loads': 0,
            'avg_load_time': 0.0,
            'current_queue_size': 0,
            'current_loading_count': 0
        }

        # Performance monitor integration
        self.performance_monitor = None

    def set_performance_monitor(self, monitor):
        """Set performance monitor for integration"""
        self.performance_monitor = monitor

    def start(self):
        """Start the queue manager"""
        if self.running:
            return

        self.running = True

        # Start worker threads
        for i in range(self.max_concurrent_loads):
            worker = threading.Thread(
                target=self._worker_thread,
                name=f"CameraLoader-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)

        # Start retry handler
        retry_worker = threading.Thread(
            target=self._retry_worker,
            name="CameraRetryHandler",
            daemon=True
        )
        retry_worker.start()
        self.worker_threads.append(retry_worker)

        print(f"🎥 Camera Loading Queue Manager started with {self.max_concurrent_loads} workers")

    def stop(self):
        """Stop the queue manager"""
        self.running = False

        # Signal all workers to stop
        for _ in range(len(self.worker_threads)):
            try:
                self.load_queue.put((0, None), timeout=1)
            except queue.Full:
                pass

        # Wait for workers to finish
        for worker in self.worker_threads:
            worker.join(timeout=5)

        print("🎥 Camera Loading Queue Manager stopped")

    def queue_camera_load(self, camera_id, camera_model, stream_type,
                         priority=0, callback=None, timeout=30.0):
        """Queue a camera for loading"""

        # Check if already loading or loaded
        if camera_id in self.loading_cameras:
            print(f"⚠️ Camera {camera_id} already loading")
            return False

        if camera_id in self.completed_cameras:
            state, _ = self.completed_cameras[camera_id]
            if state == CameraLoadState.LOADED:
                print(f"✅ Camera {camera_id} already loaded")
                return True

        # Create load request
        request = CameraLoadRequest(
            camera_id=camera_id,
            camera_model=camera_model,
            stream_type=stream_type,
            priority=priority,
            callback=callback,
            timeout=timeout
        )

        try:
            # Higher priority = lower number for PriorityQueue
            self.load_queue.put((-priority, request), timeout=1)

            with self.stats_lock:
                self.stats['total_requests'] += 1
                self.stats['current_queue_size'] = self.load_queue.qsize()

            print(f"📋 Queued camera {camera_id} for loading (priority: {priority})")
            return True

        except queue.Full:
            print(f"❌ Camera load queue full, cannot queue {camera_id}")
            return False

    def _worker_thread(self):
        """Worker thread for processing camera loads"""
        thread_name = threading.current_thread().name

        while self.running:
            try:
                # Get next request
                priority, request = self.load_queue.get(timeout=1)

                if request is None:  # Stop signal
                    break

                # Track loading state
                self.loading_cameras[request.camera_id] = request

                with self.stats_lock:
                    self.stats['current_loading_count'] = len(self.loading_cameras)
                    self.stats['current_queue_size'] = self.load_queue.qsize()

                # Notify performance monitor
                if self.performance_monitor:
                    self.performance_monitor.track_camera_loading(
                        request.camera_id, 'loading',
                        {'thread': thread_name, 'stream_type': request.stream_type}
                    )

                print(f"🔄 [{thread_name}] Loading camera {request.camera_id}")

                # Perform the actual loading
                success = self._load_camera(request)

                # Update state
                if success:
                    state = CameraLoadState.LOADED
                    with self.stats_lock:
                        self.stats['successful_loads'] += 1
                else:
                    state = CameraLoadState.FAILED
                    with self.stats_lock:
                        self.stats['failed_loads'] += 1

                # Complete the loading
                self._complete_camera_load(request, state)

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ [{thread_name}] Error in worker: {e}")
                if 'request' in locals():
                    self._complete_camera_load(request, CameraLoadState.FAILED)

    def _load_camera(self, request):
        """Actually load the camera"""
        start_time = time.time()

        try:
            # Simulate camera loading process
            # In real implementation, this would call VideoPlayerManager.create_player()

            # For demonstration, simulate variable load times
            import random
            load_time = random.uniform(1, 5)  # 1-5 seconds

            # Check for timeout
            elapsed = 0
            while elapsed < load_time and elapsed < request.timeout:
                time.sleep(0.1)
                elapsed = time.time() - start_time

                if not self.running:
                    return False

            if elapsed >= request.timeout:
                print(f"⏰ Camera {request.camera_id} load timeout after {elapsed:.1f}s")
                return False

            # Simulate success/failure
            success_rate = 0.85  # 85% success rate
            success = random.random() < success_rate

            load_duration = time.time() - start_time

            if success:
                print(f"✅ Camera {request.camera_id} loaded successfully in {load_duration:.1f}s")
            else:
                print(f"❌ Camera {request.camera_id} failed to load after {load_duration:.1f}s")

            # Update average load time
            with self.stats_lock:
                current_avg = self.stats['avg_load_time']
                total_loads = self.stats['successful_loads'] + self.stats['failed_loads']
                if total_loads > 0:
                    self.stats['avg_load_time'] = (current_avg * (total_loads - 1) + load_duration) / total_loads

            return success

        except Exception as e:
            print(f"❌ Exception loading camera {request.camera_id}: {e}")
            return False

    def _complete_camera_load(self, request, state):
        """Complete camera loading and cleanup"""
        # Remove from loading
        self.loading_cameras.pop(request.camera_id, None)

        # Add to completed
        self.completed_cameras[request.camera_id] = (state, time.time())

        # Update stats
        with self.stats_lock:
            self.stats['current_loading_count'] = len(self.loading_cameras)

        # Notify performance monitor
        if self.performance_monitor:
            self.performance_monitor.track_camera_loading(
                request.camera_id, state.value
            )

        # Call callback if provided
        if request.callback:
            try:
                request.callback(request.camera_id, state)
            except Exception as e:
                print(f"❌ Error in callback for camera {request.camera_id}: {e}")

        # Handle retries for failed loads
        if state == CameraLoadState.FAILED and request.retry_count < request.max_retries:
            request.retry_count += 1
            self.retry_queue.put(request)
            print(f"🔄 Queuing retry {request.retry_count}/{request.max_retries} for camera {request.camera_id}")

    def _retry_worker(self):
        """Worker for handling retries"""
        while self.running:
            try:
                request = self.retry_queue.get(timeout=1)

                # Wait before retry
                time.sleep(2.0 * request.retry_count)  # Exponential backoff

                # Re-queue with lower priority
                retry_priority = max(0, request.priority - request.retry_count)
                self.load_queue.put((-retry_priority, request))

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in retry worker: {e}")

    def get_stats(self):
        """Get current statistics"""
        with self.stats_lock:
            return self.stats.copy()

    def get_queue_status(self):
        """Get current queue status"""
        return {
            'queue_size': self.load_queue.qsize(),
            'loading_count': len(self.loading_cameras),
            'loading_cameras': list(self.loading_cameras.keys()),
            'completed_count': len(self.completed_cameras)
        }

# Global instance
camera_queue_manager = CameraLoadingQueueManager()
```

### 10.5 Integration với iVMS Camera System

#### 10.5.1 Cập nhật VideoPlayerManager

```python
# Trong src/common/camera/video_player_manager.py

class VideoPlayerManager(QObject):
    def __init__(self, parent=None, target: Callable = None, args=()):
        super().__init__(parent)
        self._cleanup_lock = threading.Lock()

        # 🔧 PERFORMANCE OPTIMIZATION: Giảm max_threads từ 64 xuống 8
        threadPoolManager.create_pool("VideoPlayerManager", max_threads=8)

        # 🔧 ADD: Camera queue manager
        from .camera_queue_manager import camera_queue_manager
        self.camera_queue = camera_queue_manager
        self.camera_queue.start()

        # 🔧 ADD: Performance monitor
        from .camera_threading_monitor import CameraThreadingMonitor
        self.performance_monitor = CameraThreadingMonitor()
        self.camera_queue.set_performance_monitor(self.performance_monitor)

        # Start background monitoring
        self._start_background_monitoring()

    def _start_background_monitoring(self):
        """Start background performance monitoring"""
        def monitor_worker():
            self.performance_monitor.start_monitoring(interval=5, duration=None)

        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()

    def register_player(self, widget: QWidget = None, camera_model: CameraModel = None,
                       stream_type=CommonEnum.StreamType.MAIN_STREAM):
        """🔧 OPTIMIZED: Use queue for camera loading"""

        def load_callback(camera_id, state):
            if state == CameraLoadState.LOADED:
                # Process player normally
                threadPoolManager.run("VideoPlayerManager", self.process_player,
                                    args=(widget, camera_model, stream_type))
            else:
                print(f"❌ Failed to load camera {camera_id}: {state}")

        # Queue camera loading instead of immediate processing
        camera_id = camera_model.get_property("id", None)
        priority = self._calculate_camera_priority(camera_model, stream_type)

        success = self.camera_queue.queue_camera_load(
            camera_id=camera_id,
            camera_model=camera_model,
            stream_type=stream_type,
            priority=priority,
            callback=load_callback,
            timeout=30.0
        )

        if not success:
            print(f"⚠️ Failed to queue camera {camera_id} for loading")

    def _calculate_camera_priority(self, camera_model, stream_type):
        """Calculate loading priority for camera"""
        priority = 0

        # Higher priority for main stream
        if stream_type == CommonEnum.StreamType.MAIN_STREAM:
            priority += 10

        # Higher priority for non-phone cameras (more reliable)
        if not camera_model.isPhoneCamera():
            priority += 5

        # Higher priority for cameras with permission
        if camera_model.get_property("permissionGranted", True):
            priority += 3

        return priority
```

#### 10.5.2 Enhanced LiveStreamPlayer với Performance Tracking

```python
# Trong src/common/camera/live_stream_player.py

class LiveStreamPlayer(Player):
    def __init__(self, camera_id=None, camera_model: CameraModel = None,
                 stream_type=CommonEnum.StreamType.MAIN_STREAM, height=0, width=0, uuid=''):
        super().__init__(camera_id=camera_id, camera_model=camera_model,
                        height=height, width=width, uuid=uuid)

        # 🔧 ADD: Performance tracking
        self.performance_metrics = {
            'connection_attempts': 0,
            'successful_connections': 0,
            'failed_connections': 0,
            'avg_connection_time': 0.0,
            'last_connection_time': None,
            'total_frames_processed': 0,
            'frames_dropped': 0
        }

        self.connection_start_time = None

    def connect_camera_stream(self):
        """🔧 ENHANCED: Add performance tracking"""
        self.connection_start_time = time.time()
        self.performance_metrics['connection_attempts'] += 1

        try:
            logger.debug(f'Camera {self.camera_id}: Attempting PyAV connection to {self.stream_link}')

            # 🔧 ADD: Resource check before connection
            if not self._check_system_resources():
                logger.warning(f'Camera {self.camera_id}: System resources insufficient, delaying connection')
                return False

            # Initialize PyAV wrapper with hardware acceleration enabled
            self.pyav_wrapper = PyAVWrapper()

            if self.pyav_wrapper is not None:
                # 🔧 ADD: Timeout for stream opening
                connection_success = self._open_stream_with_timeout(timeout=10.0)

                if connection_success:
                    # Update performance metrics
                    connection_time = time.time() - self.connection_start_time
                    self._update_connection_metrics(True, connection_time)

                    # Get stream info for logging
                    stream_info = self.pyav_wrapper.stream_info
                    logger.debug(f'Camera {self.camera_id}: Stream opened successfully - '
                               f'Codec: {stream_info["codec"]}, '
                               f'Resolution: {stream_info["width"]}x{stream_info["height"]}, '
                               f'FPS: {stream_info["fps"]}, '
                               f'HW Accel: {stream_info["hw_accel"]}, '
                               f'Connection Time: {connection_time:.2f}s')

                    self.camera_state = CameraState.connecting
                    self.camera_state_signal.emit(self.camera_state)
                    return True
                else:
                    self._update_connection_metrics(False, time.time() - self.connection_start_time)
                    return False
            else:
                logger.debug(f'Camera {self.camera_id}: Khởi tạo PyAVWrapper lỗi')
                self._update_connection_metrics(False, time.time() - self.connection_start_time)
                return False

        except Exception as e:
            logger.debug(f'Camera {self.camera_id}: PyAV connection failed: {str(e)}')
            self._update_connection_metrics(False, time.time() - self.connection_start_time)
            if len(self.registered_widgets) > 0:
                self.camera_state = CameraState.stopped
                self.camera_state_signal.emit(self.camera_state)
            return False

    def _check_system_resources(self):
        """Check if system has enough resources for new connection"""
        try:
            import psutil

            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > 80:
                return False

            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                return False

            # Check thread count
            process = psutil.Process()
            if process.num_threads() > 100:
                return False

            return True

        except Exception:
            return True  # If can't check, assume OK

    def _open_stream_with_timeout(self, timeout=10.0):
        """Open stream with timeout to prevent hanging"""
        import threading
        import queue

        result_queue = queue.Queue()

        def open_stream_worker():
            try:
                success = self.pyav_wrapper.open_stream(self.stream_link)
                result_queue.put(success)
            except Exception as e:
                logger.debug(f'Camera {self.camera_id}: Stream opening exception: {e}')
                result_queue.put(False)

        # Start opening in separate thread
        open_thread = threading.Thread(target=open_stream_worker, daemon=True)
        open_thread.start()

        # Wait for result with timeout
        try:
            success = result_queue.get(timeout=timeout)
            return success
        except queue.Empty:
            logger.warning(f'Camera {self.camera_id}: Stream opening timeout after {timeout}s')
            return False

    def _update_connection_metrics(self, success, connection_time):
        """Update connection performance metrics"""
        if success:
            self.performance_metrics['successful_connections'] += 1
        else:
            self.performance_metrics['failed_connections'] += 1

        # Update average connection time
        total_attempts = self.performance_metrics['connection_attempts']
        current_avg = self.performance_metrics['avg_connection_time']

        if total_attempts > 0:
            self.performance_metrics['avg_connection_time'] = (
                (current_avg * (total_attempts - 1) + connection_time) / total_attempts
            )

        self.performance_metrics['last_connection_time'] = connection_time

    def get_performance_metrics(self):
        """Get current performance metrics"""
        return self.performance_metrics.copy()
```

### 10.6 Camera Performance Dashboard

```python
#!/usr/bin/env python3
"""
Camera Performance Dashboard
Real-time dashboard cho camera system performance
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime

class CameraPerformanceDashboard:
    """
    Real-time dashboard để monitor camera performance
    """

    def __init__(self, camera_monitor, queue_manager):
        self.camera_monitor = camera_monitor
        self.queue_manager = queue_manager

        # Create GUI
        self.root = tk.Tk()
        self.root.title("Camera Performance Dashboard")
        self.root.geometry("1200x800")

        self.setup_gui()
        self.running = False

    def setup_gui(self):
        """Setup GUI components"""

        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="🎥 Camera Performance Dashboard",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))

        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Overview tab
        self.overview_frame = ttk.Frame(notebook)
        notebook.add(self.overview_frame, text="Overview")
        self.setup_overview_tab()

        # Queue tab
        self.queue_frame = ttk.Frame(notebook)
        notebook.add(self.queue_frame, text="Loading Queue")
        self.setup_queue_tab()

        # Performance tab
        self.performance_frame = ttk.Frame(notebook)
        notebook.add(self.performance_frame, text="Performance")
        self.setup_performance_tab()

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))

        self.start_button = ttk.Button(control_frame, text="Start Monitoring",
                                      command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="Stop Monitoring",
                                     command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)

        # Status label
        self.status_label = ttk.Label(control_frame, text="Status: Stopped")
        self.status_label.pack(side=tk.RIGHT)

    def setup_overview_tab(self):
        """Setup overview tab"""

        # System metrics frame
        system_frame = ttk.LabelFrame(self.overview_frame, text="System Metrics")
        system_frame.pack(fill=tk.X, padx=5, pady=5)

        self.cpu_label = ttk.Label(system_frame, text="CPU: 0%")
        self.cpu_label.pack(anchor=tk.W)

        self.memory_label = ttk.Label(system_frame, text="Memory: 0 MB")
        self.memory_label.pack(anchor=tk.W)

        self.threads_label = ttk.Label(system_frame, text="Threads: 0")
        self.threads_label.pack(anchor=tk.W)

        # Camera metrics frame
        camera_frame = ttk.LabelFrame(self.overview_frame, text="Camera Metrics")
        camera_frame.pack(fill=tk.X, padx=5, pady=5)

        self.loading_cameras_label = ttk.Label(camera_frame, text="Loading: 0")
        self.loading_cameras_label.pack(anchor=tk.W)

        self.failed_cameras_label = ttk.Label(camera_frame, text="Failed: 0")
        self.failed_cameras_label.pack(anchor=tk.W)

        self.avg_load_time_label = ttk.Label(camera_frame, text="Avg Load Time: 0s")
        self.avg_load_time_label.pack(anchor=tk.W)

        # Issues frame
        issues_frame = ttk.LabelFrame(self.overview_frame, text="Current Issues")
        issues_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.issues_text = tk.Text(issues_frame, height=10, wrap=tk.WORD)
        issues_scrollbar = ttk.Scrollbar(issues_frame, orient=tk.VERTICAL,
                                        command=self.issues_text.yview)
        self.issues_text.configure(yscrollcommand=issues_scrollbar.set)

        self.issues_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        issues_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_queue_tab(self):
        """Setup queue tab"""

        # Queue stats frame
        stats_frame = ttk.LabelFrame(self.queue_frame, text="Queue Statistics")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)

        self.queue_size_label = ttk.Label(stats_frame, text="Queue Size: 0")
        self.queue_size_label.pack(anchor=tk.W)

        self.loading_count_label = ttk.Label(stats_frame, text="Currently Loading: 0")
        self.loading_count_label.pack(anchor=tk.W)

        self.success_rate_label = ttk.Label(stats_frame, text="Success Rate: 0%")
        self.success_rate_label.pack(anchor=tk.W)

        # Loading cameras list
        loading_frame = ttk.LabelFrame(self.queue_frame, text="Currently Loading Cameras")
        loading_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.loading_tree = ttk.Treeview(loading_frame, columns=("Camera ID", "Duration", "State"))
        self.loading_tree.heading("#0", text="Index")
        self.loading_tree.heading("Camera ID", text="Camera ID")
        self.loading_tree.heading("Duration", text="Duration (s)")
        self.loading_tree.heading("State", text="State")

        loading_scrollbar = ttk.Scrollbar(loading_frame, orient=tk.VERTICAL,
                                         command=self.loading_tree.yview)
        self.loading_tree.configure(yscrollcommand=loading_scrollbar.set)

        self.loading_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        loading_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_performance_tab(self):
        """Setup performance tab"""

        # Resource pressure frame
        pressure_frame = ttk.LabelFrame(self.performance_frame, text="Resource Pressure")
        pressure_frame.pack(fill=tk.X, padx=5, pady=5)

        self.pressure_level_label = ttk.Label(pressure_frame, text="Pressure Level: UNKNOWN")
        self.pressure_level_label.pack(anchor=tk.W)

        self.cpu_trend_label = ttk.Label(pressure_frame, text="CPU Trend: UNKNOWN")
        self.cpu_trend_label.pack(anchor=tk.W)

        self.memory_trend_label = ttk.Label(pressure_frame, text="Memory Trend: UNKNOWN")
        self.memory_trend_label.pack(anchor=tk.W)

        # Performance log
        log_frame = ttk.LabelFrame(self.performance_frame, text="Performance Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.performance_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        performance_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL,
                                            command=self.performance_text.yview)
        self.performance_text.configure(yscrollcommand=performance_scrollbar.set)

        self.performance_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        performance_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_monitoring(self):
        """Start monitoring"""
        if self.running:
            return

        self.running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="Status: Running")

        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="Status: Stopped")

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Get metrics
                camera_metrics = self.camera_monitor.get_camera_performance_metrics()
                queue_stats = self.queue_manager.get_stats()
                queue_status = self.queue_manager.get_queue_status()

                # Update GUI
                self.root.after(0, self._update_gui, camera_metrics, queue_stats, queue_status)

                time.sleep(2)  # Update every 2 seconds

            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(1)

    def _update_gui(self, camera_metrics, queue_stats, queue_status):
        """Update GUI with new metrics"""

        # Update overview tab
        process = camera_metrics.get('process', {})
        self.cpu_label.config(text=f"CPU: {process.get('cpu_percent', 0):.1f}%")
        self.memory_label.config(text=f"Memory: {process.get('memory_mb', 0):.1f} MB")
        self.threads_label.config(text=f"Threads: {process.get('thread_count', 0)}")

        cameras = camera_metrics.get('cameras', {})
        loading = cameras.get('loading', {})
        self.loading_cameras_label.config(text=f"Loading: {loading.get('count', 0)}")
        self.failed_cameras_label.config(text=f"Failed: {cameras.get('failed_count', 0)}")
        self.avg_load_time_label.config(text=f"Avg Load Time: {cameras.get('avg_load_time', 0):.1f}s")

        # Update issues
        issues = self.camera_monitor.detect_camera_issues(camera_metrics)
        self.issues_text.delete(1.0, tk.END)
        if issues:
            for issue in issues:
                self.issues_text.insert(tk.END, f"[{issue['severity']}] {issue['message']}\n")
                self.issues_text.insert(tk.END, f"  → {issue['suggestion']}\n\n")
        else:
            self.issues_text.insert(tk.END, "No issues detected.")

        # Update queue tab
        self.queue_size_label.config(text=f"Queue Size: {queue_status['queue_size']}")
        self.loading_count_label.config(text=f"Currently Loading: {queue_status['loading_count']}")

        total_requests = queue_stats['total_requests']
        successful = queue_stats['successful_loads']
        success_rate = (successful / total_requests * 100) if total_requests > 0 else 0
        self.success_rate_label.config(text=f"Success Rate: {success_rate:.1f}%")

        # Update loading cameras tree
        for item in self.loading_tree.get_children():
            self.loading_tree.delete(item)

        for i, camera_id in enumerate(queue_status['loading_cameras']):
            self.loading_tree.insert("", tk.END, text=str(i+1),
                                   values=(camera_id, "Loading...", "LOADING"))

        # Update performance tab
        pressure = camera_metrics.get('pressure', {})
        if pressure.get('status') == 'ANALYZED':
            self.pressure_level_label.config(text=f"Pressure Level: {pressure.get('pressure_level', 'UNKNOWN')}")
            trends = pressure.get('trends', {})
            self.cpu_trend_label.config(text=f"CPU Trend: {trends.get('cpu', 'UNKNOWN')}")
            self.memory_trend_label.config(text=f"Memory Trend: {trends.get('memory', 'UNKNOWN')}")

        # Add performance log entry
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] CPU: {process.get('cpu_percent', 0):.1f}%, " \
                   f"Memory: {process.get('memory_mb', 0):.0f}MB, " \
                   f"Loading: {loading.get('count', 0)}, " \
                   f"Queue: {queue_status['queue_size']}\n"

        self.performance_text.insert(tk.END, log_entry)
        self.performance_text.see(tk.END)

        # Keep only last 100 lines
        lines = self.performance_text.get(1.0, tk.END).split('\n')
        if len(lines) > 100:
            self.performance_text.delete(1.0, f"{len(lines)-100}.0")

    def run(self):
        """Run the dashboard"""
        self.root.mainloop()

# Usage example
if __name__ == "__main__":
    from camera_threading_monitor import CameraThreadingMonitor
    from camera_queue_manager import CameraLoadingQueueManager

    monitor = CameraThreadingMonitor()
    queue_manager = CameraLoadingQueueManager()
    queue_manager.start()

    dashboard = CameraPerformanceDashboard(monitor, queue_manager)
    dashboard.run()
```

### 10.7 Practical Usage Instructions

#### 10.7.1 Development Usage

```bash
# 1. Monitor camera threading performance
python camera_threading_monitor.py --duration 300

# 2. Run performance dashboard
python camera_performance_dashboard.py

# 3. Test camera loading with queue
python test_camera_queue.py --cameras 20 --concurrent 4
```

#### 10.7.2 Production Integration

```python
# Trong main application startup
def initialize_camera_performance_monitoring():
    """Initialize camera performance monitoring"""

    # Start camera queue manager
    from src.common.camera.camera_queue_manager import camera_queue_manager
    camera_queue_manager.start()

    # Start performance monitoring if enabled
    if os.getenv('CAMERA_MONITORING_ENABLED', 'false').lower() == 'true':
        from src.common.camera.camera_threading_monitor import CameraThreadingMonitor
        monitor = CameraThreadingMonitor()
        camera_queue_manager.set_performance_monitor(monitor)

        # Start background monitoring
        def monitor_worker():
            monitor.start_monitoring(interval=10, duration=None)

        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()

        print("🎥 Camera performance monitoring enabled")

# Call during app initialization
initialize_camera_performance_monitoring()
```

### 10.8 Key Benefits

✅ **Controlled Resource Usage**: Queue manager giới hạn concurrent camera loading
✅ **Performance Monitoring**: Real-time tracking của camera loading performance
✅ **Issue Detection**: Tự động phát hiện stuck cameras, resource pressure
✅ **UI Responsiveness**: Prevent UI blocking khi load nhiều camera
✅ **Retry Logic**: Intelligent retry cho failed camera connections
✅ **Priority System**: Load important cameras trước
✅ **Resource Awareness**: Check system resources trước khi load camera

**Tài liệu này cung cấp framework hoàn chỉnh để monitoring performance với overhead tối thiểu, tương thích PyInstaller, phù hợp cho cả development và production environments trên tất cả platforms.**
```
```
