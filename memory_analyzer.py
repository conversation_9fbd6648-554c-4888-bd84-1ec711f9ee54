#!/usr/bin/env python3
"""
Memory Analyzer for Scalene Profile
Tập trung phân tích memory/RAM usage và validation tổng memory
"""

import json
import sys
from pathlib import Path
from collections import defaultdict
import psutil

class MemoryAnalyzer:
    def __init__(self, profile_file):
        self.profile_file = profile_file
        self.profile_data = None
        self.memory_summary = {
            'total_allocated': 0,
            'total_peak': 0,
            'total_growth': 0,
            'allocation_count': 0,
            'by_file': defaultdict(lambda: {'allocated': 0, 'peak': 0, 'count': 0}),
            'by_function': [],
            'by_line': [],
            'large_allocations': [],  # >50MB
            'frequent_allocations': [],  # >100 allocations
        }
    
    def load_profile(self):
        """Load profile data"""
        try:
            with open(self.profile_file, 'r') as f:
                self.profile_data = json.load(f)
            return True
        except Exception as e:
            print(f"❌ Error loading {self.profile_file}: {e}")
            return False
    
    def get_system_memory_baseline(self):
        """Lấy baseline memory của system"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            
            return {
                'process_rss_mb': memory_info.rss / 1024 / 1024,
                'process_vms_mb': memory_info.vms / 1024 / 1024,
                'system_total_gb': system_memory.total / 1024 / 1024 / 1024,
                'system_available_gb': system_memory.available / 1024 / 1024 / 1024,
                'system_used_percent': system_memory.percent
            }
        except:
            return None
    
    def analyze_memory_allocations(self):
        """Phân tích chi tiết memory allocations"""
        files = self.profile_data.get('files', {})
        
        for file_path, file_data in files.items():
            filename = Path(file_path).name
            file_memory = self.memory_summary['by_file'][filename]
            
            # Analyze functions
            functions = file_data.get('functions', [])
            for func in functions:
                malloc_mb = func.get('n_malloc_mb', 0)
                peak_mb = func.get('n_peak_mb', 0)
                growth_mb = func.get('n_growth_mb', 0)
                mallocs = func.get('n_mallocs', 0)
                
                if malloc_mb > 0:
                    self.memory_summary['total_allocated'] += malloc_mb
                    self.memory_summary['total_peak'] += peak_mb
                    self.memory_summary['total_growth'] += growth_mb
                    self.memory_summary['allocation_count'] += mallocs
                    
                    file_memory['allocated'] += malloc_mb
                    file_memory['peak'] += peak_mb
                    file_memory['count'] += mallocs
                    
                    func_info = {
                        'file': filename,
                        'function': func.get('line', 'unknown'),
                        'line': func.get('lineno', 0),
                        'malloc_mb': malloc_mb,
                        'peak_mb': peak_mb,
                        'growth_mb': growth_mb,
                        'mallocs': mallocs,
                        'avg_mb': func.get('n_avg_mb', 0),
                        'python_fraction': func.get('n_python_fraction', 0)
                    }
                    
                    self.memory_summary['by_function'].append(func_info)
                    
                    # Large allocations
                    if malloc_mb > 50:
                        self.memory_summary['large_allocations'].append(func_info)
                    
                    # Frequent allocations
                    if mallocs > 100:
                        self.memory_summary['frequent_allocations'].append(func_info)
            
            # Analyze lines
            lines = file_data.get('lines', [])
            for line in lines:
                malloc_mb = line.get('n_malloc_mb', 0)
                peak_mb = line.get('n_peak_mb', 0)
                growth_mb = line.get('n_growth_mb', 0)
                mallocs = line.get('n_mallocs', 0)
                lineno = line.get('lineno', 0)
                code = line.get('line', '').strip()
                
                if malloc_mb > 0 and code and not code.startswith('#'):
                    # Note: Đã tính trong functions rồi, chỉ track lines riêng biệt
                    line_info = {
                        'file': filename,
                        'line': lineno,
                        'code': code[:80] + '...' if len(code) > 80 else code,
                        'malloc_mb': malloc_mb,
                        'peak_mb': peak_mb,
                        'growth_mb': growth_mb,
                        'mallocs': mallocs,
                        'avg_mb': line.get('n_avg_mb', 0)
                    }
                    
                    self.memory_summary['by_line'].append(line_info)
    
    def validate_memory_totals(self):
        """Validate memory totals với system memory"""
        system_memory = self.get_system_memory_baseline()
        profile_runtime = self.profile_data.get('elapsed_time_sec', 0)
        
        print("🔍 MEMORY VALIDATION")
        print("=" * 60)
        
        if system_memory:
            print(f"📊 Current Process Memory: {system_memory['process_rss_mb']:.1f} MB RSS")
            print(f"📊 System Memory: {system_memory['system_used_percent']:.1f}% used")
            print(f"📊 Available: {system_memory['system_available_gb']:.1f} GB")
        
        print(f"📊 Profile Runtime: {profile_runtime:.1f} seconds")
        print(f"📊 Total Allocated (Scalene): {self.memory_summary['total_allocated']:.1f} MB")
        print(f"📊 Total Peak (Scalene): {self.memory_summary['total_peak']:.1f} MB")
        print(f"📊 Total Growth: {self.memory_summary['total_growth']:.1f} MB")
        print(f"📊 Total Allocations: {self.memory_summary['allocation_count']}")
        
        # Validation warnings
        if system_memory:
            if self.memory_summary['total_allocated'] > system_memory['process_rss_mb'] * 2:
                print("⚠️  WARNING: Scalene total > 2x current process memory")
                print("   → Possible memory was freed during profiling")
                print("   → Or allocations are cumulative over time")
            
            if self.memory_summary['total_peak'] > system_memory['process_rss_mb'] * 5:
                print("⚠️  WARNING: Peak memory very high vs current usage")
                print("   → Check for memory spikes during profiling")
    
    def print_memory_breakdown(self):
        """In breakdown chi tiết memory usage"""
        
        print("\n💾 MEMORY BREAKDOWN BY FILE")
        print("-" * 60)
        
        # Sort files by memory usage
        sorted_files = sorted(
            self.memory_summary['by_file'].items(),
            key=lambda x: x[1]['allocated'],
            reverse=True
        )
        
        for filename, stats in sorted_files[:10]:
            if stats['allocated'] > 0:
                print(f"📁 {filename}")
                print(f"   Allocated: {stats['allocated']:.1f} MB")
                print(f"   Peak: {stats['peak']:.1f} MB")
                print(f"   Allocations: {stats['count']}")
                print()
        
        print("\n🔥 TOP MEMORY ALLOCATIONS (Functions)")
        print("-" * 60)
        
        # Sort functions by allocation
        sorted_functions = sorted(
            self.memory_summary['by_function'],
            key=lambda x: x['malloc_mb'],
            reverse=True
        )
        
        for i, func in enumerate(sorted_functions[:10], 1):
            severity = "🔴" if func['malloc_mb'] > 100 else "🟡" if func['malloc_mb'] > 50 else "🟢"
            print(f"{severity} #{i} {func['file']}:{func['line']}")
            print(f"   Function: {func['function']}")
            print(f"   Allocated: {func['malloc_mb']:.1f} MB")
            print(f"   Peak: {func['peak_mb']:.1f} MB")
            print(f"   Growth: {func['growth_mb']:.1f} MB")
            print(f"   Allocations: {func['mallocs']}")
            print(f"   Python Fraction: {func['python_fraction']:.3f}")
            print()
        
        print("\n📊 LARGE ALLOCATIONS (>50MB)")
        print("-" * 60)
        
        if self.memory_summary['large_allocations']:
            for alloc in self.memory_summary['large_allocations']:
                print(f"🔴 {alloc['file']}:{alloc['line']} - {alloc['malloc_mb']:.1f} MB")
                print(f"   {alloc['function']}")
        else:
            print("✅ No large allocations detected")
        
        print("\n🔄 FREQUENT ALLOCATIONS (>100 calls)")
        print("-" * 60)
        
        if self.memory_summary['frequent_allocations']:
            for alloc in self.memory_summary['frequent_allocations']:
                print(f"🟡 {alloc['file']}:{alloc['line']} - {alloc['mallocs']} allocations")
                print(f"   {alloc['function']} ({alloc['malloc_mb']:.1f} MB total)")
        else:
            print("✅ No frequent allocations detected")
    
    def print_memory_recommendations(self):
        """In recommendations cho memory optimization"""
        
        print("\n💡 MEMORY OPTIMIZATION RECOMMENDATIONS")
        print("-" * 60)
        
        total_mb = self.memory_summary['total_allocated']
        large_count = len(self.memory_summary['large_allocations'])
        frequent_count = len(self.memory_summary['frequent_allocations'])
        
        if total_mb > 1000:  # >1GB
            print("🔴 HIGH MEMORY USAGE (>1GB allocated)")
            print("   → Implement memory pooling for large objects")
            print("   → Consider streaming instead of loading full data")
            print("   → Monitor for memory leaks")
        elif total_mb > 500:  # >500MB
            print("🟡 MODERATE MEMORY USAGE (>500MB allocated)")
            print("   → Monitor memory growth patterns")
            print("   → Consider object reuse strategies")
        
        if large_count > 0:
            print(f"\n🔴 {large_count} LARGE ALLOCATIONS detected:")
            print("   → Review camera frame buffer management")
            print("   → Implement lazy loading for video data")
            print("   → Consider compression for stored frames")
        
        if frequent_count > 0:
            print(f"\n🟡 {frequent_count} FREQUENT ALLOCATIONS detected:")
            print("   → Implement object pooling")
            print("   → Cache frequently used objects")
            print("   → Reduce allocation frequency in loops")
        
        # Specific recommendations for VMS
        print("\n📋 VMS-SPECIFIC RECOMMENDATIONS:")
        print("   → Limit concurrent camera loading to reduce memory spikes")
        print("   → Implement frame buffer pooling for video processing")
        print("   → Monitor memory during camera group operations")
        print("   → Set memory limits per camera stream")
        print("   → Use weak references for camera objects when possible")
    
    def save_memory_report(self, output_file):
        """Lưu detailed memory report"""
        report = {
            'analysis_time': self.profile_data.get('elapsed_time_sec', 0),
            'memory_summary': {
                'total_allocated_mb': self.memory_summary['total_allocated'],
                'total_peak_mb': self.memory_summary['total_peak'],
                'total_growth_mb': self.memory_summary['total_growth'],
                'allocation_count': self.memory_summary['allocation_count']
            },
            'by_file': dict(self.memory_summary['by_file']),
            'top_functions': sorted(
                self.memory_summary['by_function'],
                key=lambda x: x['malloc_mb'],
                reverse=True
            )[:20],
            'large_allocations': self.memory_summary['large_allocations'],
            'frequent_allocations': self.memory_summary['frequent_allocations'],
            'system_memory': self.get_system_memory_baseline()
        }
        
        try:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Memory report saved to: {output_file}")
        except Exception as e:
            print(f"❌ Error saving report: {e}")
    
    def run_analysis(self, output_file=None):
        """Chạy phân tích memory hoàn chỉnh"""
        if not self.load_profile():
            return False
        
        print("🔬 MEMORY ANALYSIS FOR VMS APPLICATION")
        print("=" * 60)
        
        # Analyze allocations
        self.analyze_memory_allocations()
        
        # Validate totals
        self.validate_memory_totals()
        
        # Print breakdown
        self.print_memory_breakdown()
        
        # Print recommendations
        self.print_memory_recommendations()
        
        # Save report
        if output_file:
            self.save_memory_report(output_file)
        
        return True

def main():
    if len(sys.argv) < 2:
        print("Usage: python memory_analyzer.py <profile.json> [output.json]")
        sys.exit(1)
    
    profile_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(profile_file).exists():
        print(f"❌ File not found: {profile_file}")
        sys.exit(1)
    
    analyzer = MemoryAnalyzer(profile_file)
    success = analyzer.run_analysis(output_file)
    
    if success:
        print("\n✅ Memory analysis completed!")
    else:
        print("\n❌ Memory analysis failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
