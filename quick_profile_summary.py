#!/usr/bin/env python3
"""
Quick Profile Summary
Tạo báo c<PERSON>o ngắn gọn từ Scalene profile.json
"""

import json
import sys
from pathlib import Path

def quick_analyze(profile_file):
    """Quick analysis của profile.json"""
    
    try:
        with open(profile_file, 'r') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ Error reading {profile_file}: {e}")
        return
    
    print("🔬 SCALENE PROFILE QUICK SUMMARY")
    print("=" * 50)
    
    # Basic info
    elapsed = data.get('elapsed_time_sec', 0)
    samples = data.get('alloc_samples', 0)
    print(f"⏱️  Runtime: {elapsed:.1f}s")
    print(f"📊 Samples: {samples}")
    
    # Collect all performance data
    cpu_hotspots = []
    memory_hotspots = []
    gpu_hotspots = []
    
    files = data.get('files', {})
    for file_path, file_data in files.items():
        filename = Path(file_path).name
        
        # Check functions
        for func in file_data.get('functions', []):
            cpu_py = func.get('n_cpu_percent_python', 0)
            cpu_c = func.get('n_cpu_percent_c', 0)
            malloc = func.get('n_malloc_mb', 0)
            gpu_pct = func.get('n_gpu_percent', 0)
            gpu_mem = func.get('n_gpu_avg_memory_mb', 0)
            
            if cpu_py > 0.5:  # >0.5% CPU
                cpu_hotspots.append({
                    'file': filename,
                    'line': func.get('lineno', 0),
                    'name': func.get('line', 'unknown'),
                    'cpu_py': cpu_py,
                    'cpu_c': cpu_c
                })
            
            if malloc > 10:  # >10MB
                memory_hotspots.append({
                    'file': filename,
                    'line': func.get('lineno', 0),
                    'name': func.get('line', 'unknown'),
                    'malloc': malloc,
                    'peak': func.get('n_peak_mb', 0)
                })
            
            if gpu_pct > 5 or gpu_mem > 100:  # >5% GPU or >100MB
                gpu_hotspots.append({
                    'file': filename,
                    'line': func.get('lineno', 0),
                    'name': func.get('line', 'unknown'),
                    'gpu_pct': gpu_pct,
                    'gpu_mem': gpu_mem
                })
        
        # Check lines
        for line in file_data.get('lines', []):
            cpu_py = line.get('n_cpu_percent_python', 0)
            malloc = line.get('n_malloc_mb', 0)
            lineno = line.get('lineno', 0)
            code = line.get('line', '').strip()
            
            if cpu_py > 0.5 and code and not code.startswith('#'):
                cpu_hotspots.append({
                    'file': filename,
                    'line': lineno,
                    'name': code[:50] + '...' if len(code) > 50 else code,
                    'cpu_py': cpu_py,
                    'cpu_c': line.get('n_cpu_percent_c', 0)
                })
            
            if malloc > 10 and code and not code.startswith('#'):
                memory_hotspots.append({
                    'file': filename,
                    'line': lineno,
                    'name': code[:50] + '...' if len(code) > 50 else code,
                    'malloc': malloc,
                    'peak': line.get('n_peak_mb', 0)
                })
    
    # Sort and display top issues
    cpu_hotspots.sort(key=lambda x: x['cpu_py'], reverse=True)
    memory_hotspots.sort(key=lambda x: x['malloc'], reverse=True)
    gpu_hotspots.sort(key=lambda x: x['gpu_pct'], reverse=True)
    
    # CPU Report
    print(f"\n🔥 TOP CPU HOTSPOTS ({len(cpu_hotspots)} total)")
    print("-" * 50)
    for i, item in enumerate(cpu_hotspots[:5], 1):
        severity = "🔴" if item['cpu_py'] > 2 else "🟡"
        print(f"{severity} #{i} {item['file']}:{item['line']}")
        print(f"   {item['cpu_py']:.2f}% Python + {item['cpu_c']:.2f}% C/Native")
        print(f"   {item['name']}")
        print()
    
    # Memory Report
    print(f"\n💾 TOP MEMORY HOTSPOTS ({len(memory_hotspots)} total)")
    print("-" * 50)
    for i, item in enumerate(memory_hotspots[:5], 1):
        severity = "🔴" if item['malloc'] > 100 else "🟡"
        print(f"{severity} #{i} {item['file']}:{item['line']}")
        print(f"   {item['malloc']:.1f}MB allocated, {item['peak']:.1f}MB peak")
        print(f"   {item['name']}")
        print()
    
    # GPU Report
    if gpu_hotspots:
        print(f"\n🎮 TOP GPU HOTSPOTS ({len(gpu_hotspots)} total)")
        print("-" * 50)
        for i, item in enumerate(gpu_hotspots[:5], 1):
            severity = "🔴" if item['gpu_pct'] > 20 else "🟡"
            print(f"{severity} #{i} {item['file']}:{item['line']}")
            print(f"   {item['gpu_pct']:.1f}% GPU, {item['gpu_mem']:.1f}MB memory")
            print(f"   {item['name']}")
            print()
    
    # Summary & Recommendations
    print("\n📋 QUICK RECOMMENDATIONS")
    print("-" * 50)
    
    if cpu_hotspots:
        max_cpu = cpu_hotspots[0]['cpu_py']
        if max_cpu > 5:
            print("🔴 HIGH CPU USAGE detected!")
            print("   → Profile with py-spy for detailed analysis")
            print("   → Consider async/threading optimization")
        elif max_cpu > 1:
            print("🟡 MODERATE CPU USAGE detected")
            print("   → Monitor during peak camera loading")
    
    if memory_hotspots:
        max_mem = memory_hotspots[0]['malloc']
        if max_mem > 200:
            print("🔴 HIGH MEMORY ALLOCATION detected!")
            print("   → Check for memory leaks")
            print("   → Implement object pooling")
        elif max_mem > 50:
            print("🟡 MODERATE MEMORY USAGE detected")
            print("   → Monitor camera loading patterns")
    
    if gpu_hotspots:
        print("🎮 GPU USAGE detected")
        print("   → Monitor GPU memory growth")
        print("   → Consider GPU memory pooling")
    
    if not cpu_hotspots and not memory_hotspots and not gpu_hotspots:
        print("✅ No significant performance issues detected!")
        print("   → Performance looks good")
        print("   → Consider testing with more cameras")
    
    print(f"\n📊 SUMMARY: {len(cpu_hotspots)} CPU, {len(memory_hotspots)} Memory, {len(gpu_hotspots)} GPU hotspots")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python quick_profile_summary.py <profile.json>")
        sys.exit(1)
    
    profile_file = sys.argv[1]
    if not Path(profile_file).exists():
        print(f"❌ File not found: {profile_file}")
        sys.exit(1)
    
    quick_analyze(profile_file)
